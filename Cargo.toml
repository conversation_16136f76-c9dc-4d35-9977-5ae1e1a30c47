[workspace]
resolver = "3" # Add resolver version
members = [
    "helpers/tamtil",
    "helpers/tamtil/jawad"
]

[workspace.package]
edition = "2024"

[workspace.dependencies]
tokio = "1.45.1"
hyper = "1.6.0"
rkyv = { version = "0.8.10", features = ["bytecheck"] }
http-body-util = "0.1.3"
hyper-util = "0.1.13"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
serde = "1.0.219"
rmp-serde = "1.3.0"
uuid = "1.17.0"
bytes = "1.10.1"
bincode = "2.0.1"
rmpv = "1.3.0"
serde_json = "1.0.140"
thiserror = "2.0.12"
chrono = "0.4.41"
tungstenite = "0.26.2"
tokio-tungstenite = "0.26.2"
futures = "0.3.31"
toml = "0.8.22"
url = "2.5.4"
criterion = "0.6.0"
anyhow = "1.0.98"
async-trait = "0.1.88"
pyo3 = "0.25.0"
pyo3-async-runtimes = "0.25"
tokio-test = "0.4.4"
tempfile = "3.20.0"
embed-resource = "3.0.3"
petgraph = "0.8.1"
omnipaxos = "0.2.2"
omnipaxos_storage = "0.2.2"
quinn = "0.11"
tokio-util = "0.7"
rustls = "0.23"
rcgen = "0.13"
webpki-roots = "1.0.0"
rand = "0.9"
rusqlite = "0.36.0"
sled = "0.34.7"
redb = "2.6.0"
blake3 = "1.5"
crossbeam = "0.8"
dashmap = "6.1.0"
crc32fast = "1.4"
num_cpus = "1.17.0"
base64 = "0.22"
prometheus = "0.14.0"
lazy_static = "1.5.0"
glommio = "0.9.0"