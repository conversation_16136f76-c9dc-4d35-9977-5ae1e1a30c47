---
# It should be *very* clear that this is an insecure, dev-only configuration. Don't run this in production!
server:
  http_listen_port: 3100
schema_config:
  configs:
    - from: 2021-08-01
      store: tsdb
      object_store: s3
      schema: v12
      index:
        prefix: index_
        period: 24h
common:
  path_prefix: /loki
  replication_factor: 1
  storage:
    s3:
      endpoint: minio:9000
      insecure: true
      bucketnames: loki-data
      access_key_id: loki
      secret_access_key: supersecret
      s3forcepathstyle: true
  ring:
    instance_addr: 0.0.0.0
    kvstore:
      store: memberlist
ruler:
  storage:
    s3:
      bucketnames: loki-ruler
