altnames.cnf
.DS_Store
.backup*_test.json
.backup_test.db
/target
/insecure
**/*.rs.bk
test.db
cargo_vendor_config
/vendor
rlm_python/test_data/certs/
rlm_python/test_data/ca.pem
scripts/loc.sh
vendor.tar.*
*.patch
tools/orca/example_profiles/small/orca-edited.toml
/docs/
# webui things we don't need
*.d.ts
server/web_ui/*/pkg/*.js

# coverage-related things
*.profraw
tarpaulin-report.html

# kanidm simple packaging
deployment-config/
kanidm_simple_pkg/
kanidm-client-tools.tar.gz

# python things
**/__pycache__/**
**/.venv/**
.coverage
pykanidm/dist/
pykanidm/site/

# oauth2 integration test things
scripts/oauth_proxy/client.secret
scripts/oauth_proxy/envfile

# local config things
.envrc

# IDEs
.idea/
.vscode/

# javascript test things
node_modules/