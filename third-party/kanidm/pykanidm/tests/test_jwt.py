"""Testing JWS things things"""

from datetime import datetime, timezone
import logging

import pytest

from kanidm.tokens import J<PERSON>, TokenStore


TEST_TOKEN = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"  # noqa: E501 pylint: disable=line-too-long


def test_jws_parser() -> None:
    """tests the parsing"""

    expected_header = {
        "alg": "ES256",
        "jwk": {
            "kty": "EC",
            "crv": "P-256",
            "x": "mJA8-MD_xTqApfIOglZm5rzEhhCxCv4qdSixlcWT7fk",
            "y": "7-2VCncHw4AuYRiaZXOahUtF1A6H7wyLkQmEzGnKJJs",
            "alg": "ES256",
            "use": "sig",
        },
        "typ": "JWT",
    }

    expected_payload = {
        "session_id": "f1198673-4b90-4618-bde7-10bcc6c8c8a4",
        "auth_type": "generatedpassword",
        "expiry": [2022, 265, 28366, 802525000],
        "uuid": "00000000-0000-0000-0000-000000000018",
        "name": "idm_admin",
        "displayname": "IDM Administrator",
        "spn": "idm_admin@localhost",
        "mail_primary": None,
        "lim_uidx": False,
        "lim_rmax": 128,
        "lim_pmax": 256,
        "lim_fmax": 32,
    }

    test_jws = JWS(TEST_TOKEN)

    assert test_jws.header.model_dump() == expected_header
    assert test_jws.payload.model_dump() == expected_payload


def test_tokenstuff() -> None:
    """tests stuff"""

    logging.basicConfig(level=logging.DEBUG, force=True)

    data = {
        "instances": {
            "": {
                "tokens": {
                    "idm_admin": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"  # noqa: E501 pylint: disable=line-too-long
                }
            }
        }
    }

    token_store = TokenStore.model_validate(data)
    print(token_store.model_dump_json(indent=2))

    info = token_store.token_info("idm_admin")
    print(f"Parsed token: {info}")
    if info is None:
        pytest.skip("No token!")  # type: ignore[call-non-callable]
    print(info.expiry_datetime)
    assert (
        datetime(
            year=2022,
            month=9,
            day=22,
            hour=9,
            minute=25,
            second=23,
            tzinfo=timezone.utc,
        )
        == info.expiry_datetime
    )
