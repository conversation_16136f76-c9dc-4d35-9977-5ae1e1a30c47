"""tests the check_vlan function"""

import asyncio
from typing import Any

import pytest

from kanidm import KanidmClient
from kanidm.types import KanidmClientConfig, RadiusTokenGroup

from kanidm.radius.utils import check_vlan


@pytest.mark.asyncio
async def test_check_vlan() -> None:
    """test 1"""

    # event_loop = asyncio.get_running_loop()

    testconfig = KanidmClientConfig.parse_toml(
        """
    uri='https://kanidm.example.com'
    radius_groups = [
        { spn = "<EMAIL>", "vlan" = 1234 },
        { spn = "hello@world", "vlan" = 12345 },
    ]
    """
    )

    print(f"{testconfig=}")

    kanidm_client = KanidmClient(
        config=testconfig,
    )
    print(f"{kanidm_client.config=}")

    assert (
        check_vlan(
            acc=12345678,
            group=RadiusTokenGroup(spn="<EMAIL>", uuid="crabz"),
            kanidm_client=kanidm_client,
        )
        == 1234
    )

    assert (
        check_vlan(
            acc=12345678,
            group=RadiusTokenGroup(spn="<EMAIL>", uuid="lol"),
            kanidm_client=kanidm_client,
        )
        == 12345678
    )
