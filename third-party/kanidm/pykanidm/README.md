# kanidm

A Python module for interacting with Kanidm.

Currently in very very very early beta, please
[log an issue](https://github.com/kanidm/kanidm/issues/new/choose) for feature requests and bugs.

## Installation

```bash
python -m pip install kanidm
```

## Documentation

Documentation can be generated by [cloning the repository](https://github.com/kanidm/kanidm) and
running `make docs/pykanidm/build`. The documentation will appear in `./pykanidm/site`. You'll need
make and the [uv](https://pypi.org/project/uv/) package installed.

## Testing

Set up your dev environment using `uv` - `python -m pip install uv && uv sync`.

Pytest it used for testing, if you don't have a live server to test against and config set up, use
`uv run pytest -m 'not network'`.

## Changelog

| Version | Date       | Notes                                                 |
| ------- | ---------- | ----------------------------------------------------- |
| 0.0.1   | 2022-08-16 | Initial release                                       |
| 0.0.2   | 2022-08-16 | Updated license, including test code in package       |
| 0.0.3   | 2022-08-17 | Updated test suite to allow skipping of network tests |
| 1.2.0   | 2025-05-13 | Replaced poetry with uv for packaging |
