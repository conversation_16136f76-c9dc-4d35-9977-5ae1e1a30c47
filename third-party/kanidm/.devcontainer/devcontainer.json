// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/rust
{
	"name": "Rust",

	// https://github.com/devcontainers/images/tree/main/src/rust
	"image": "mcr.microsoft.com/devcontainers/rust:1-1-bookworm",
	"features": {
	},

	// Use 'mounts' to make the cargo cache persistent in a Docker Volume.
	"mounts": [
		{
			"source": "devcontainer-cargo-cache-${devcontainerId}",
			"target": "/usr/local/cargo",
			"type": "volume"
		},
		{
			"source": "devcontainer-sccache-cache-${devcontainerId}",
			"target": "/home/<USER>/.cache/sccache",
			"type": "volume"
		}
	],
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [8443],
	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "./scripts/devcontainer_postcreate.sh",

	// postStartCommand runs things once the container's up, in this case, we're starting sccache
	"postStartCommand": "./scripts/devcontainer_poststart.sh",

	// environment variables
	"containerEnv": {
		// "RUSTC_WRAPPER": "sccache",
		// "CC":"/usr/bin/sccache /usr/bin/clang",
		"SCCACHE_SERVER_UDS" : "/tmp/sccache.sock"
	}

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
