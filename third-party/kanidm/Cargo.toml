[workspace.package]
version = "1.7.0-dev"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
rust-version = "1.80"
edition = "2021"
license = "MPL-2.0"
homepage = "https://github.com/kanidm/kanidm/"
repository = "https://github.com/kanidm/kanidm/"

[workspace]
resolver = "2"
members = [
    "proto",
    "tools/cli",
    "tools/device_flow",
    "tools/iam_migrations/freeipa",
    "tools/iam_migrations/ldap",
    "tools/orca",
    "unix_integration/common",
    "unix_integration/nss_kanidm",
    "unix_integration/pam_kanidm",
    "unix_integration/resolver",
    "server/daemon",
    "server/lib",
    "server/lib-macros",
    "server/core",
    "server/testkit",
    "server/testkit-macros",
    "libs/client",
    "libs/crypto",
    "libs/file_permissions",
    "libs/profiles",
    "libs/scim_proto",
    "libs/sketching",
    "libs/users",
]

# Below follows some guides/estimates to system resources consumed during a build.
# Most of these values are over-estimates and are just rough observations.
#
# These were tested on a 10core M1 Max.
# Parallel Linking Maximum = an estimate of how much ram will be consumed at peak
#   while the build is linking multiple binaries in parallel
# Single Largest Binary Maximum = an estamite of how much ram is conusmed by the
#   single largest binary during linking. This would reflect a single threaded
#   build ram maximum.
# Time = estimate on how long the build may take.

# cargo build --release
#
# Parallel Linking Maximum: 6GB
# Single Largest Binary Maximum: 5GB
# Time: ~6 minutes
[profile.release]
debug = true
strip = "none"
lto = "thin"
opt-level = 3
codegen-units = 32

[profile.release.build-override]
debug = false
opt-level = 0
codegen-units = 256

# cargo build --profile release-lto
#
# Parallel Linking Maximum: 24GB
# Single Largest Binary Maximum: 16GB
# Time: ~11 minutes
[profile.release-lto]
inherits = "release"
lto = "fat"
codegen-units = 1

# cargo build
#
# Parallel Linking Maximum: 4GB
# Single Largest Binary Maximum: 3GB
# Time: ~2 minutes
#
# cargo test [-- --test-threads=1]
#
# Parallel Maximum: 1GB
# Single Maximum: 0.2GB
[profile.dev]
debug = true
lto = false
opt-level = 0
codegen-units = 256

[profile.dev.build-override]
debug = true
opt-level = 0
codegen-units = 256

[patch.crates-io]
## As Kanidm maintains a number of libraries, sometimes during development we need to override them
## with local or git versions. This patch table allows quick uncommenting to achieve that.

# compact_jwt = { path = "../compact-jwt" }
# concread = { path = "../concread" }

# idlset = { path = "../idlset" }

# ldap3_client = { path = "../ldap3/client" }
# ldap3_proto = { path = "../ldap3/proto" }
# ldap3_client = { git = "https://github.com/kanidm/ldap3.git" }
# ldap3_proto = { git = "https://github.com/kanidm/ldap3.git" }

# base64urlsafedata = { path = "../webauthn-rs/base64urlsafedata" }
# webauthn-authenticator-rs = { path = "../webauthn-rs/webauthn-authenticator-rs" }
# webauthn-rs = { path = "../webauthn-rs/webauthn-rs" }
# webauthn-rs-core = { path = "../webauthn-rs/webauthn-rs-core" }
# webauthn-rs-proto = { path = "../webauthn-rs/webauthn-rs-proto" }
# sshkey-attest = { path = "../webauthn-rs/sshkey-attest" }

# For BSD nss support
libnss = { git = "https://github.com/Firstyear/libnss-rs.git", branch = "20250207-freebsd" }
# Allow ssh keys to have comments with spaces.
sshkeys = { git = "https://github.com/Firstyear/rust-sshkeys.git", rev = "49cb53232115d3aea86cd059b151e376293805fc" }
# Branch currently carrying some needed rs256 signer patches for jwk handling,
# as main is currently working to drop openssl and may need more work before
# we commit to that change here.
compact_jwt = { git = "https://github.com/Firstyear/compact-jwt.git", rev = "043976842773dd035fe394261347edeb644e3091" }

[workspace.dependencies]
kanidmd_core = { path = "./server/core", version = "=1.7.0-dev" }
kanidmd_lib = { path = "./server/lib", version = "=1.7.0-dev" }
kanidmd_lib_macros = { path = "./server/lib-macros", version = "=1.7.0-dev" }
kanidmd_testkit = { path = "./server/testkit", version = "=1.7.0-dev" }
kanidm_build_profiles = { path = "./libs/profiles", version = "=1.7.0-dev" }
kanidm_client = { path = "./libs/client", version = "=1.7.0-dev" }
kanidm-hsm-crypto = "^0.2.0"
kanidm_lib_crypto = { path = "./libs/crypto", version = "=1.7.0-dev" }
kanidm_lib_file_permissions = { path = "./libs/file_permissions", version = "=1.7.0-dev" }
kanidm_proto = { path = "./proto", version = "=1.7.0-dev" }
kanidm_unix_common = { path = "./unix_integration/common", version = "=1.7.0-dev" }
kanidm_utils_users = { path = "./libs/users", version = "=1.7.0-dev" }
scim_proto = { path = "./libs/scim_proto", version = "=1.7.0-dev" }
sketching = { path = "./libs/sketching", version = "=1.7.0-dev" }

anyhow = { version = "1.0.98" }
argon2 = { version = "0.5.3", features = ["alloc"] }
askama = { version = "0.12.1", features = ["serde", "with-axum"] }
askama_axum = { version = "0.4.0" }
async-trait = "^0.1.85"
axum = { version = "0.7.9", features = [
    "form",
    "json",
    "macros",
    "multipart",
    "original-uri",
    "query",
    "tokio",
    "tracing",
] }
axum-extra = { version = "0.9.6", features = ["cookie"] }
axum-macros = "0.4.2"
axum-htmx = { version = "0.5.0", features = ["serde", "guards"] }
base32 = "^0.5.1"
base64 = "^0.22.1"
base64urlsafedata = "0.5.1"
bitflags = "^2.9.1"
bytes = "^1.9.0"
cidr = "0.3.1"
clap = { version = "4.5.38", features = ["derive", "env"] }
clap_complete = "^4.5.50"
# Forced by saffron/cron
chrono = "^0.4.39"
compact_jwt = { version = "^0.4.2", default-features = false }
concread = "^0.5.5"
cron = "0.15.0"
crossbeam = "0.8.4"
csv = "1.3.1"
dialoguer = "0.11.0"
dhat = "0.3.3"
dyn-clone = "^1.0.17"
enum-iterator = "2.1.0"
filetime = "^0.2.24"
fs4 = "^0.13.0"
futures = "^0.3.31"
futures-util = { version = "^0.3.30", features = ["sink"] }
gix = { version = "0.71.0", default-features = false }
haproxy-protocol = { version = "0.0.1" }
hashbrown = { version = "0.15.2", features = ["serde", "inline-more"] }
hex = "^0.4.3"
http = "1.2.0"
http-body-util = "0.1"
hyper = { version = "1.5.1", features = [
    "full",
] } # hyper full includes client/server/http2
hyper-util = { version = "0.1.12", features = ["server", "tokio"] }
idlset = "^0.2.5"
image = { version = "0.25.6", default-features = false, features = [
    "gif",
    "jpeg",
    "webp",
] }
itertools = "0.14.0"
jsonschema = "0.30.0"
kanidmd_web_ui_shared = { path = "./server/web_ui/shared" }
# REMOVE this
lazy_static = "^1.5.0"
ldap3_client = "^0.5.2"
ldap3_proto = { version = "^0.5.2", features = ["serde"] }

libc = "0.2.172"
libnss = "^0.8.0"
libsqlite3-sys = "^0.25.2"
lodepng = "3.11.0"
lru = "0.14.0"
mathru = "0.15.5"
md4 = "0.10.2"
md-5 = "0.10.6"
mimalloc = "0.1.46"
notify-debouncer-full = { version = "0.5" }
num_enum = "0.7.3"
oauth2_ext = { version = "^4.4.2", package = "oauth2", default-features = false }
openssl-sys = "^0.9"
openssl = "^0.10.72"

opentelemetry = { version = "0.27.0" }
opentelemetry_api = { version = "0.27.0", features = ["logs", "metrics"] }
opentelemetry-otlp = { version = "0.27.0", default-features = false, features = [
    "serde",
    "logs",
    "metrics",
    "http-proto",
    "grpc-tonic",
] }
opentelemetry_sdk = { version = "0.27.0", features = ["rt-tokio"] }
opentelemetry-semantic-conventions = "0.27.0"
tracing-opentelemetry = "0.28.0"
tracing-core = "0.1.33"

peg = "0.8"
pkg-config = "^0.3.31"
prctl = "1.0.0"
proc-macro2 = "1.0.95"
qrcode = "0.14.1"
quote = "1"
rand = "0.9.1"
rand_chacha = "0.9.0"
regex = "1.11.0"
reqwest = { version = "0.12.12", default-features = false, features = [
    "cookies",
    "http2",
    "json",
    "gzip",
    "rustls-tls-native-roots",
    "rustls-tls-native-roots-no-provider",
] }
rusqlite = { version = "0.35.0", features = ["array", "bundled"] }
rustls = { version = "0.23.27", default-features = false, features = [
    "aws_lc_rs",
] }

sd-notify = "^0.4.5"
selinux = "^0.5.1"
serde = "^1.0.217"
serde_cbor = { version = "0.12.0-dev", package = "serde_cbor_2" }
serde_json = "^1.0.137"
serde_urlencoded = "^0.7.1"
serde_with = "3.12.0"
sha-crypt = "0.5.0"
sha2 = "0.10.8"
shellexpand = "3.1.1"
smartstring = "^1.0.1"
smolset = "^1.3.1"
sshkey-attest = "^0.5.0"
sshkeys = "0.3.3"
svg = "0.18.0"
syn = { version = "2.0.100", features = ["full"] }
tempfile = "3.20.0"
testkit-macros = { path = "./server/testkit-macros" }
time = { version = "^0.3.36", features = ["formatting", "local-offset"] }

tokio = "^1.45.1"
tokio-openssl = "^0.6.5"
tokio-util = "^0.7.13"

toml = "^0.8.20"
tracing = { version = "^0.1.41", features = [
    "max_level_trace",
    "release_max_level_debug",
] }
tracing-subscriber = { version = "^0.3.19", features = ["env-filter"] }
tracing-forest = "^0.1.6"

url = "^2.5.2"
urlencoding = "2.1.3"
utoipa = { version = "4.2.0", features = ["url", "uuid"] }
utoipa-swagger-ui = "6.0.0"
uuid = "^1.17.0"

webauthn-authenticator-rs = { version = "0.5.1", features = [
    "softpasskey",
    "softtoken",
] }
webauthn-rs = { version = "0.5.1", features = ["preview-features"] }
webauthn-rs-core = "0.5.1"
webauthn-rs-proto = "0.5.1"

whoami = "^1.6.0"
walkdir = "2"

x509-cert = "0.2.5"

zxcvbn = "3.1.0"

nonempty = "0.11.0"
