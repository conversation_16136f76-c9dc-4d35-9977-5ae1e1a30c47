#!/bin/sh

# PROVIDE: kanidm_unixd_tasks
# REQUIRE: LOGIN
# KEYWORD: shutdown
#
# Add these lines to /etc/rc.conf.local or /etc/rc.conf
# to enable this service:
#
# kanidm_unixd_tasks_enable (bool):	Set to NO by default.
#				Set it to YES to enable kanidm_unixd_tasks.

. /etc/rc.subr

name=kanidm_unixd_tasks
rcvar=kanidm_unixd_tasks_enable

load_rc_config $name

: ${kanidm_unixd_tasks_enable:="NO"}

pidfile="/var/run/kanidm-unixd-tasks.pid"
command=/usr/sbin/daemon
command_args="-u root -p /var/run/kanidm-unixd-tasks.pid -T kanidm_unixd_tasks /usr/local/libexec/${name}"
procname=/usr/local/libexec/${name}

run_rc_command "$1"
