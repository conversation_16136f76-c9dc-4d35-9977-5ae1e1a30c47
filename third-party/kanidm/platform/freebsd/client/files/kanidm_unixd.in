#!/bin/sh

# PROVIDE: kanidm_unixd
# REQUIRE: LOGIN
# KEYWORD: shutdown
#
# Add these lines to /etc/rc.conf.local or /etc/rc.conf
# to enable this service:
#
# kanidm_unixd_enable (bool):	Set to NO by default.
#				Set it to YES to enable kanidm_unixd.

. /etc/rc.subr

name=kanidm_unixd
rcvar=kanidm_unixd_enable

load_rc_config $name

: ${kanidm_unixd_enable:="NO"}

pidfile="/var/run/kanidm-unixd.pid"
command=/usr/sbin/daemon
command_args="-u _kanidm_unixd -p /var/run/kanidm-unixd.pid -T kanidm_unixd /usr/local/libexec/${name}"
procname=/usr/local/libexec/${name}

run_rc_command "$1"
