# You should not need to edit this file. Instead, use a drop-in file as described in:
#   /usr/lib/systemd/system/kanidmd.service.d/custom.conf

[Unit]
Description=Kanidm Identity Server
After=time-sync.target network-online.target
Wants=time-sync.target network-online.target
Before=radiusd.service

[Service]
Type=notify-reload
DynamicUser=yes
StateDirectory=kanidm
StateDirectoryMode=0750
CacheDirectory=kanidmd
CacheDirectoryMode=0750
RuntimeDirectory=kanidmd
RuntimeDirectoryMode=0755
ExecStart=/usr/sbin/kanidmd server -c /etc/kanidm/server.toml

AmbientCapabilities=CAP_NET_BIND_SERVICE
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

# If OOM occurs, request a clean stop
OOMPolicy=stop
# Adjust our weight toward *not* being killed under pressure.
OOMScoreAdjust=-100

NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHostname=true
ProtectClock=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
ProtectControlGroups=true
MemoryDenyWriteExecute=true

[Install]
WantedBy=multi-user.target
