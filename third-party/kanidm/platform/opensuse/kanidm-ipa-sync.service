# You should not need to edit this file. Instead, use a drop-in file as described in:
#   /usr/lib/systemd/system/kanidmd.service.d/custom.conf

[Unit]
Description=Kanidm IPA Sync Service
After=time-sync.target network-online.target
Wants=time-sync.target network-online.target

[Service]
Type=exec
DynamicUser=yes
LoadCredential=config:/etc/kanidm/ipa-sync
Environment=KANIDM_IPA_SYNC_CONFIG=%d/config
ExecStart=/usr/sbin/kanidm-ipa-sync --schedule

AmbientCapabilities=CAP_NET_BIND_SERVICE
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHostname=true
ProtectClock=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
ProtectControlGroups=true
MemoryDenyWriteExecute=true

[Install]
WantedBy=multi-user.target
