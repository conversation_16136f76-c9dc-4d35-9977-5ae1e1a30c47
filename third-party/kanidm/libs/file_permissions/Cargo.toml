[package]
name = "kanidm_lib_file_permissions"
description = "Kanidm File Permissions Library"
# documentation = "https://docs.rs/kanidm_proto/latest/kanidm_proto/"
version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
test = true
doctest = false

[dependencies]

[target.'cfg(not(target_family = "windows"))'.dependencies]
kanidm_utils_users = { workspace = true }
