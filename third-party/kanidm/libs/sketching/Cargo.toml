[package]
name = "sketching"
description = "Logging crate"
# We do not have tests in this pkg
autotests = false

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
test = false
doctest = false

[dependencies]
num_enum = { workspace = true }
opentelemetry = { workspace = true, features = ["metrics"] }
opentelemetry-otlp = { workspace = true, default-features = false, features = [
    "serde",
    "logs",
    "metrics",
    "http-proto",
    "grpc-tonic",
] }
opentelemetry_sdk = { workspace = true, features = ["rt-tokio"] }
opentelemetry-semantic-conventions = { workspace = true }

serde = { workspace = true, features = ["derive"] }
tracing = { workspace = true, features = ["attributes"] }
tracing-core = { workspace = true }
tracing-forest = { workspace = true, features = [
    "uuid",
    "smallvec",
    "tokio",
    "env-filter",
] }
tracing-opentelemetry = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
