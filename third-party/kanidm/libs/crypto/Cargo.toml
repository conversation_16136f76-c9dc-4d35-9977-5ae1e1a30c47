[package]
name = "kanidm_lib_crypto"
version = { workspace = true }
edition = { workspace = true }
description = "Kanidm cryptographic functionality"
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[features]
tpm = ["kanidm-hsm-crypto/tpm"]

[lib]
test = true
doctest = false

[dependencies]
argon2 = { workspace = true }
base64 = { workspace = true }
base64urlsafedata = { workspace = true }
hex = { workspace = true }
kanidm_proto = { workspace = true }
kanidm-hsm-crypto = { workspace = true }

# We need to explicitly ask for openssl-sys so that we get the version propagated
# into the build.rs for legacy feature checks.
openssl-sys = { workspace = true }
openssl = { workspace = true }
rand = { workspace = true }
md4 = { workspace = true }
sha2 = { workspace = true }
serde = { workspace = true, features = ["derive"] }
tracing = { workspace = true }
uuid = { workspace = true }
x509-cert = { workspace = true, features = ["pem"] }

md-5 = { workspace = true }
sha-crypt = { workspace = true }

[dev-dependencies]
sketching = { workspace = true }


[package.metadata.cargo-machete]
ignored = ["openssl-sys"]
