[package]
name = "scim_proto"
description = "Kanidm SCIM Protocol Bindings"
documentation = "https://docs.rs/kanidm_client/latest/kanidm_client/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
test = true
doctest = false

[dependencies]
base64urlsafedata = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }
peg = { workspace = true }
time = { workspace = true, features = [
    "local-offset",
    "formatting",
    "parsing",
    "serde",
] }
url = { workspace = true, features = ["serde"] }
utoipa = { workspace = true }
uuid = { workspace = true, features = ["serde"] }
[dev-dependencies]
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
