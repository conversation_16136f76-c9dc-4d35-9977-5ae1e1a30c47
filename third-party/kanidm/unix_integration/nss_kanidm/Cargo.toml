[package]
name = "nss_kanidm"
description = "Kanidm NSS module"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
name = "nss_kanidm"
crate-type = ["cdylib"]
path = "src/lib.rs"

[dependencies]
kanidm_unix_common = { workspace = true }

[target.'cfg(not(target_family = "windows"))'.dependencies]
libnss = { workspace = true }
libc = { workspace = true }
lazy_static = { workspace = true }

[target."cfg(target_os = \"freebsd\")".build-dependencies]
cc = "^1.2.24"

## Debian packaging
[package.metadata.deb]
name = "libnss-kanidm"
maintainer = "<PERSON> <<EMAIL>>"
depends = ""
section = "network"
priority = "optional"
assets = [
    [
        "target/release/libnss_kanidm.so",
        # This is not the final path as cargo-deb needs to be run with --multiarch=foreign
        "usr/lib/libnss_kanidm.so.2",
        "644",
    ],
]

[package.metadata.cargo-machete]
ignored = ["cc", "lazy_static"]
