#!/bin/sh
# postinst script for kanidm-unixd
#
# see: dh_installdeb(1)

set -e


case "$1" in
    configure)
        echo "============================="
        echo "Thanks for installing Kanidm!"
        echo "============================="
        echo "Please ensure you modify the configuration files at /etc/kanidm/unixd and /etc/kanidm/config"
        echo "Full examples are in /usr/share/kanidm-unixd/"
        echo "PAM has already been autoconfigured by the libpam-kanidm package. To configure nsswitch, please follow instructions in https://kanidm.github.io/kanidm/master/integrations/pam_and_nsswitch.html"
    ;;

    abort-upgrade|abort-remove|abort-deconfigure)
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
