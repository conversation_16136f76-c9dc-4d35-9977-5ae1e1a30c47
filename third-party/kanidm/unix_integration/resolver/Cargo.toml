[package]
name = "kanidm_unix_int"
description = "Kanidm Unix Integration Clients"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[features]
default = ["unix"]
unix = []
selinux = ["dep:selinux", "kanidm_unix_common/selinux"]
tpm = ["kanidm-hsm-crypto/tpm", "kanidm_unix_common/tpm"]
dhat-heap = ["dep:dhat"]
dhat-ad-hoc = ["dep:dhat"]

[[bin]]
name = "kanidm_unixd"
path = "src/bin/kanidm_unixd.rs"
required-features = ["unix"]
test = false
doctest = false

[[bin]]
name = "kanidm_unixd_tasks"
path = "src/bin/kanidm_unixd_tasks.rs"
required-features = ["unix"]
test = false
doctest = false

[[bin]]
name = "kanidm_ssh_authorizedkeys"
path = "src/bin/kanidm_ssh_authorizedkeys.rs"
required-features = ["unix"]
test = false
doctest = false

[[bin]]
name = "kanidm-unix"
path = "src/bin/kanidm-unix.rs"
required-features = ["unix"]
test = false
doctest = false

[lib]
name = "kanidm_unix_resolver"
path = "src/lib.rs"
test = true
doctest = false

[dependencies]
async-trait = { workspace = true }
bytes = { workspace = true }
clap = { workspace = true, features = ["derive", "env"] }
dialoguer = { workspace = true }
dhat = { workspace = true, optional = true }
futures = { workspace = true }
hashbrown = { workspace = true }
libc = { workspace = true }
lru = { workspace = true }
kanidm_client = { workspace = true }
kanidm_proto = { workspace = true }
kanidm-hsm-crypto = { workspace = true }
kanidm_lib_crypto = { workspace = true }
kanidm_lib_file_permissions = { workspace = true }
kanidm_unix_common = { workspace = true }
notify-debouncer-full = { workspace = true }
rusqlite = { workspace = true }
selinux = { workspace = true, optional = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
sketching = { workspace = true }
time = { workspace = true, features = ["std"] }
toml = { workspace = true }
tokio = { workspace = true, features = [
    "rt",
    "fs",
    "macros",
    "sync",
    "time",
    "net",
    "io-util",
] }
tokio-util = { workspace = true, features = ["codec"] }
tracing = { workspace = true }
uuid = { workspace = true }
walkdir = { workspace = true }

[target.'cfg(target_os = "linux")'.dependencies]
sd-notify = { workspace = true }
prctl = { workspace = true }

[target.'cfg(not(target_family = "windows"))'.dependencies]
kanidm_utils_users = { workspace = true }

[target.'cfg(not(target_os = "illumos"))'.dependencies]
mimalloc = { workspace = true }

[dev-dependencies]
kanidmd_core = { workspace = true }
kanidmd_testkit = { workspace = true }

[build-dependencies]
clap = { workspace = true, features = ["derive"] }
clap_complete = { workspace = true }
kanidm_build_profiles = { workspace = true }

## Debian packaging
# We only package unixd itself here as shared libraries must be packaged separarately due to multiarch rules.
[package.metadata.deb]
name = "kanidm-unixd"
maintainer = "James Hodgkinson <<EMAIL>>"
# Can't use $auto depends because the name of libssl3 varies by distro and version
depends = [
	"libc6",
	"tpm-udev",
	"libssl3 | libssl3t64",
	"libpam-kanidm",
	"libnss-kanidm"
]
section = "network"
priority = "optional"
changelog = "../../target/debian/changelog"  # Generated by platform/debian/build_debs.sh
assets = [
    [ "target/release/kanidm_ssh_authorizedkeys", "usr/sbin/", "755" ],
    [ "target/release/kanidm_unixd_tasks", "usr/sbin/", "755" ],
    [ "target/release/kanidm_unixd", "usr/sbin/", "755" ],
    [ "target/release/kanidm-unix", "usr/sbin/", "755" ],
    [ "../../examples/unixd-safe-default", "etc/kanidm/unixd", "644" ],
    [ "../../examples/kanidm-safe-default", "etc/kanidm/config", "644" ],
    [ "../../examples/kanidm", "usr/share/kanidm-unixd/", "444" ],
    [ "../../examples/unixd", "usr/share/kanidm-unixd/", "444" ],
]
maintainer-scripts = "debian/"
systemd-units = [
	{ unit-name = "kanidm-unixd", enable = true},
	{ unit-name = "kanidm-unixd-tasks", enable = true},
]

[package.metadata.cargo-machete]
ignored = ["kanidm_build_profiles", "clap_complete"]
