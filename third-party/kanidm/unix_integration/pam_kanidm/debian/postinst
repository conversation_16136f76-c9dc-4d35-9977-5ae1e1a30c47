#!/bin/sh
# postinst script for libpam-kanidm
#
# see: dh_installdeb(1)

set -e


case "$1" in
    configure)
        echo "Updating PAM configuration"
        pam-auth-update --package
    ;;

    abort-upgrade|abort-remove|abort-deconfigure)
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
