[package]
name = "kanidm_unix_common"
description = "Kanidm Unix Resolver - Common Libraries"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[features]
default = ["unix"]
unix = []
selinux = ["dep:selinux"]
tpm = []

[lib]
name = "kanidm_unix_common"
path = "src/lib.rs"
test = true
doctest = false

[dependencies]
bytes = { workspace = true }
csv = { workspace = true }
futures = { workspace = true }
kanidm_proto = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_with = { workspace = true }
sha-crypt = { workspace = true }
tokio = { workspace = true, features = ["time", "net", "macros"] }
tokio-util = { workspace = true, features = ["codec"] }
toml = { workspace = true }
tracing = { workspace = true }

selinux = { workspace = true, optional = true }

[target.'cfg(not(target_family = "windows"))'.dependencies]
kanidm_utils_users = { workspace = true }

[build-dependencies]
kanidm_build_profiles = { workspace = true }

[package.metadata.cargo-machete]
ignored = ["kanidm_build_profiles"]
