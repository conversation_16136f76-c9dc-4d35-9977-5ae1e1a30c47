[kanidm_client]
url = "https://localhost:8443"
strict = false
ca = "/data/ca.crt"
user = "radius_service_account"
secret = "XRELDJUh2pk6RcxRzgScKLOAQd7hNk3RZHe73gFo8BM8D3Iq"

# default vlans for groups that don't specify one.
[DEFAULT]
vlan = 1

# [group.test]
# vlan =

[radiusd]
ca = "/data/certs/ca.pem"
key =  '/data/certs/key.pem'
cert = "/data/certs/cert.pem"
dh = "/data/certs/dh"
required_group = "radius_access_allowed"

[client.localhost]
ipaddr = "127.0.0.1"
secret = "testing123"

[client.docker]
ipaddr = "**********/16"
secret = "testing123"

