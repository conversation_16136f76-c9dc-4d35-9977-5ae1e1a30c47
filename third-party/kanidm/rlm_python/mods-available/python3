#
# Make sure the PYTHONPATH environmental variable contains the
# directory(s) for the modules listed below.
#
# Uncomment any func_* which are included in your module. If
# rlm_python is called for a section which does not have
# a function defined, it will return NOOP.
#
python3 {
	#  Path to the python modules
	#
	#  Note that due to limitations on Python, this configuration
	#  item is GLOBAL TO THE SERVER.  That is, you cannot have two
	#  instances of the python module, each with a different path.
	#
	python_path="/usr/lib64/python3.8:/usr/lib/python3.8:/usr/lib/python3.8/site-packages:/usr/lib64/python3.8/site-packages:/usr/lib64/python3.8/lib-dynload:/usr/local/lib/python3.8/site-packages:/etc/raddb/mods-config/python3/"

	module = "kanidm.radius"
	# python_path = ${modconfdir}/${.:name}

	# Pass all VPS lists as a 6-tuple to the callbacks
	# (request, reply, config, state, proxy_req, proxy_reply)
	# pass_all_vps = no

	# Pass all VPS lists as a dictionary to the callbacks
	# Keys: "request", "reply", "config", "session-state", "proxy-request",
	#       "proxy-reply"
	# This option prevales over "pass_all_vps"
	# pass_all_vps_dict = no

	mod_instantiate = ${.module}
	func_instantiate = instantiate

	#mod_detach = ${.module}
        #func_detach = detach

	mod_authorize = ${.module}
	func_authorize = authorize

	mod_authenticate = ${.module}
	func_authenticate = authenticate

	#mod_preacct = ${.module}
	#func_preacct = preacct

	#mod_accounting = ${.module}
	#func_accounting = accounting

	#mod_checksimul = ${.module}
	#func_checksimul = checksimul

	#mod_pre_proxy = ${.module}
	#func_pre_proxy = pre_proxy

	#mod_post_proxy = ${.module}
	#func_post_proxy = post_proxy

	#mod_post_auth = ${.module}
	#func_post_auth = post_auth

	#mod_recv_coa = ${.module}
	#func_recv_coa = recv_coa

	#mod_send_coa = ${.module}
	#func_send_coa = send_coa
}
