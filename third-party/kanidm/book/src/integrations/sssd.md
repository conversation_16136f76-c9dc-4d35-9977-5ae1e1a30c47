# SSSD

[SSSD](https://sssd.io/) is an alternative [PAM and nsswitch](./pam_and_nsswitch.md) provider that
is commonly available on Linux.

> [!WARNING]
>
> SSSD should be considered a "last resort". If possible, always use the native Kanidm pam and
> nsswitch tools instead.

## Limitations

SSSD has many significant limitations compared to Kanidm's native
[PAM and nsswitch](./pam_and_nsswitch.md) provider.

### Performance

Kanidm's native provider outperforms SSSD significantly for both online and offline user resolving
and operations. Because of this, SSSD can cause higher load on the Kanidm server due to its design
limitations.

### Features

SSSD is not able to access all of the features of Kanidm, limiting the integration options available
to you.

### Security

By default Kanidm uses state of the art cryptographic methods with configurable TPM binding of
cached local credentials. SSSD uses significantly weaker methods to cache passwords. This means that
you should not be caching credentials with SSSD, limiting deployment flexibility.

In addition, Kanidm's providers are written in Rust rather than C, meaning they have less surface
area for attack and compromise. These providers have been through multiple security audits performed
by the SUSE product security teams.

## Support

If you choose to use the SSSD provider the Kanidm project will only provide "best effort" for
compatibility and issue resolution.

## Configuration

An example configuration for SSSD is provided.

```toml
# Example configuration for SSSD to resolve accounts via Kanidm
#
# This should always be a "last resort". If possible you should always use the
# kanidm pam and nsswitch resolver as these will give you a better and more
# reliable setup.
#
# Changing the values of this config is not recommended.
#
# Support for environments using SSSD is "best effort".

# Setup for ssh keys
# Inside /etc/ssh/sshd_config add the lines:
#   AuthorizedKeysCommand /usr/bin/sss_ssh_authorizedkeys %u
#   AuthorizedKeysCommandUser nobody
# You can test with the command: sss_ssh_authorizedkeys <username>

[sssd]
services = nss, pam, ssh
config_file_version = 2

domains = ldap

[nss]
homedir_substring = /home

[domain/ldap]
# Uncomment this for more verbose logging.
# debug_level=3

id_provider = ldap
auth_provider = ldap
access_provider = ldap
chpass_provider = ldap
ldap_schema = rfc2307bis
ldap_search_base = o=idm

# Your URI must be LDAPS. Kanidm does not support StartTLS.
ldap_uri = ldaps://idm.example.com

# These allow SSSD to resolve user primary groups, which in Kanidm are implied by
# the existence of the user. Ensure you change the search base to your ldap_search_base.
ldap_group_object_class = object
ldap_group_search_base = o=idm?subtree?(|(objectClass=posixAccount)(objectClass=posixGroup))

# To use cacert dir, place *.crt files in this path then run:
# /usr/bin/openssl rehash /etc/openldap/certs
# or (for older versions of openssl)
# /usr/bin/c_rehash /etc/openldap/certs
# ldap_tls_cacertdir = /etc/openldap/certs

# Path to the cacert
# ldap_tls_cacert = /etc/openldap/certs/ca.crt

# Only users who match this filter can login and authorise to this machine. Note
# that users who do NOT match, will still have their uid/gid resolve, but they
# can't login.
#
# Note that because of how Kanidm presents group names, this value SHOULD be an SPN
ldap_access_filter = (memberof=<EMAIL>)

# Set the home dir override. Kanidm does not support configuration of homedirs as an
# attribute, and will use the uid number of the account. This is because users can
# change their uid at anytime, so you must have home directories configured in a stable
# way that does not change.
#
# Beware, than SSSD will incorrectly treat this value as a signed integer rather than unsigned
# so some users will have a -uidnumber instead.
override_homedir = /home/<USER>

# This prevents an issue where SSSD incorrectly attempts to recursively walk all
# entries in Kanidm.
#
# ⚠️  NEVER CHANGE THIS VALUE ⚠️
ignore_group_members = True

# Disable caching of credentials by SSSD. SSSD uses less secure local password storage
# mechanisms, and is a risk for credential disclosure.
#
# ⚠️  NEVER CHANGE THIS VALUE ⚠️
cache_credentials = False
```
