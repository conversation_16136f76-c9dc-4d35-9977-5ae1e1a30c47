[package]
name = "orca"
description = "Orca - load testing for LDAP and Kanidm"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[[bin]]
name = "orca"
path = "src/main.rs"
test = false
doctest = false

[dependencies]
async-trait = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true }
crossbeam = { workspace = true }
csv = { workspace = true }
hashbrown = { workspace = true }
idlset = { workspace = true }
kanidm_client = { workspace = true }
mathru = { workspace = true }
rand = { workspace = true }
rand_chacha = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread", "sync"] }
toml = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[target.'cfg(not(any(target_family = "windows", target_os = "illumos")))'.dependencies]
mimalloc = { workspace = true }

[build-dependencies]
kanidm_build_profiles = { workspace = true }

[package.metadata.cargo-machete]
ignored = ["kanidm_build_profiles"]
