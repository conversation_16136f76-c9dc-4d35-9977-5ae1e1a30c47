#!/bin/sh
# postinst script for kanidm
#
# see: dh_installdeb(1)

set -e

case "$1" in
    configure)
        if [ ! -f /etc/kanidm/config ]; then
            echo "============================="
            echo "Thanks for installing Kanidm!"
            echo "============================="
            echo "Please ensure you create a configuration file at /etc/kanidm/config or ~/.config/kanidm. An example file is located in /usr/share/kanidm/"
        fi
    ;;

    abort-upgrade|abort-remove|abort-deconfigure)
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
