[package]
name = "kanidm_tools"
default-run = "kanidm"
description = "Kanidm Client Tools"
documentation = "https://kanidm.github.io/kanidm/stable/"
version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[features]
default = [
    "unix",
    # "dev-oauth2-device-flow"
]
unix = []
dev-oauth2-device-flow = []

[lib]
name = "kanidm_cli"
path = "src/cli/lib.rs"
test = false
doctest = false

[[bin]]
name = "kanidm"
path = "src/cli/main.rs"
doc = false
test = false
doctest = false

[[bin]]
name = "kanidm_ssh_authorizedkeys_direct"
path = "src/ssh_authorizedkeys.rs"
test = true
doctest = false

[dependencies]
anyhow = { workspace = true }
clap = { workspace = true, features = ["derive", "env"] }
compact_jwt = { workspace = true, features = ["openssl"] }
dialoguer = { workspace = true }
libc = { workspace = true }
kanidm_client = { workspace = true }
kanidm_lib_file_permissions = { workspace = true }
kanidm_proto = { workspace = true }
qrcode = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
shellexpand = { workspace = true }
time = { workspace = true, features = ["serde", "std", "local-offset"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "fmt"] }
tokio = { workspace = true, features = ["rt", "macros", "fs", "signal"] }
url = { workspace = true, features = ["serde"] }
uuid = { workspace = true }
zxcvbn = { workspace = true }
lazy_static = { workspace = true }
regex = { workspace = true }

[dev-dependencies]
sketching = { workspace = true }

[build-dependencies]
kanidm_proto = { workspace = true }
clap = { workspace = true, features = ["derive"] }
clap_complete = { workspace = true }
kanidm_build_profiles = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
uuid = { workspace = true }
url = { workspace = true }
time = { workspace = true }

## See src/cli/webauthn/mod.rs for which features are
## required for which target_os here
[target."cfg(target_os = \"windows\")".dependencies.webauthn-authenticator-rs]
workspace = true
features = ["win10"]

[target."cfg(target_os = \"linux\")".dependencies.webauthn-authenticator-rs]
workspace = true
features = ["u2fhid"]

[target."cfg(target_os = \"macos\")".dependencies.webauthn-authenticator-rs]
workspace = true
features = ["mozilla"]
# features = ["u2fhid"]

[target."cfg(target_os = \"freebsd\")".dependencies.webauthn-authenticator-rs]
workspace = true
features = ["mozilla"]

[target."cfg(target_os = \"openbsd\")".dependencies.webauthn-authenticator-rs]
workspace = true
features = ["mozilla"]

## Debian packaging
[package.metadata.deb]
name = "kanidm"
maintainer = "James Hodgkinson <<EMAIL>>"
# Can't use $auto depends because the name of libssl3 varies by distro and version
depends = ["libc6", "libudev1", "libssl3 | libssl3t64"]
section = "admin"
priority = "optional"
changelog = "../../target/debian/changelog" # Generated by packaging scripts
assets = [
    [
        "target/release/kanidm",
        "usr/bin/",
        "755",
    ],
    [
        "target/release/kanidm_ssh_authorizedkeys_direct",
        "usr/bin/",
        "755",
    ],
    [
        "../../examples/config",
        "usr/share/kanidm/",
        "444",
    ],
]
maintainer-scripts = "debian/"

[package.metadata.cargo-machete]
ignored = ["clap_complete", "kanidm_build_profiles"]
