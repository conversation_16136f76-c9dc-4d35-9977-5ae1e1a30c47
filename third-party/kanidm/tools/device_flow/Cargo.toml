[package]
name = "kanidm_device_flow"
description = "Kanidm Device Flow Example Client"
documentation = "https://kanidm.github.io/kanidm/stable/"
version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }


[lib]
doctest = false
test = false

[features]

[dependencies]
anyhow = { workspace = true }
kanidm_proto = { workspace = true }
oauth2 = "5.0.0"
reqwest = { workspace = true, default-features = false, features = [
    "rustls-tls",
] }
sketching = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tracing = { workspace = true }
