---
name: <PERSON><PERSON> checks

# Trigger the workflow on push
"on":
  push:
  pull_request:

env:
  SCCACHE_GHA_ENABLED: "true"
  RUSTC_WRAPPER: "sccache"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  clippy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup sccache
        uses: mozilla-actions/sccache-action@v0.0.9
      - name: Install dependencies
        run: |
          sudo apt-get update && \
          sudo apt-get install -y \
            libpam0g-dev \
            libselinux1-dev \
            libssl-dev \
            libsystemd-dev \
            libtss2-dev \
            libudev-dev \
            pkg-config \
            tpm-udev
      - name: "Run clippy"
        run: cargo clippy --lib --bins --examples --all-features
  fmt:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup sccache
        uses: mozilla-actions/sccache-action@v0.0.9
      - name: "Run cargo fmt"
        run: cargo fmt --check
