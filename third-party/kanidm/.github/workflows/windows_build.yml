---
name: Windows Build and Test

# Trigger the workflow on push to master or pull request
"on":
  push:
    branches:
      - master
  pull_request:
  workflow_dispatch:  # so you can run it manually
env:
  SCCACHE_GHA_ENABLED: "true"
  RUSTC_WRAPPER: "sccache"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  windows_build_kanidm:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install OpenSSL
        run: |
          vcpkg integrate install
          vcpkg install openssl:x64-windows-static-md
      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
      - name: Setup sccache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo build --locked -p kanidm_client -p kanidm_tools --bin kanidm
      # yamllint disable-line rule:line-length
      - run: cargo test -p kanidm_client -p kanidm_tools
