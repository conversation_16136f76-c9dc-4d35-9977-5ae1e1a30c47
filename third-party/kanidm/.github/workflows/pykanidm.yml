---
name: PyKanidm tests

"on":
  push:
    branches:
      - "master"
  pull_request:
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  python_tests:
    runs-on: ubuntu-latest
    env:
      UV_LINK_MODE: "copy"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0    # Fetch all history for .GitInfo and .Lastmod
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Install uv
        run: pip install --root-user-action=ignore  uv
      - name: Running mypy
        run: |
          cd pykanidm
          uv run mypy --strict kanidm tests
          uv run ty check
      - name: Running Linting
        run: |
          cd pykanidm
          uv run ruff check kanidm tests
      - name: Running pytest
        run: |
          cd pykanidm
          uv run pytest -v -m 'not network'
