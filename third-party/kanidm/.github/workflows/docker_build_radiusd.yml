---
name: Container - Radiusd

# This is always built and uploads an OCI image as a build artifact, but only
# pushes to "ghcr.io/kanidm/radius:devel" when on "kanidm/kanidm@master".
"on":
  pull_request:
  push:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  set_tag_values:
    runs-on: ubuntu-latest
    name: Set image tag values
    steps:
      - id: step1
        run: |
          echo "OWNER_LC=${OWNER,,}" >> "${GITHUB_OUTPUT}"
        env:
          OWNER: '${{ github.repository_owner }}'
      - id: step2
        run: |
          echo "REF_NAME=$(echo ${REF_NAME,,} | awk -F/ '{print $1}')" >> "${GITHUB_OUTPUT}"
        env:
          REF_NAME: '${{ github.ref_name }}'
    outputs:
      owner_lc: ${{ steps.step1.outputs.OWNER_LC }}
      ref_name: ${{ steps.step2.outputs.REF_NAME }}

  radius_build:
    name: Build radius Docker image
    runs-on: ubuntu-latest
    needs: set_tag_values
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v5
      - name: Build radius
        uses: docker/build-push-action@v6
        with:
          platforms: linux/arm64,linux/amd64
          tags: ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/radius:devel,ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/radius:${{ needs.set_tag_values.outputs.ref_name}}
          file: rlm_python/Dockerfile
          context: .
          labels: ${{ steps.meta.outputs.labels }}
          annotations: ${{ steps.meta.outputs.annotations }}
          # Must use OCI exporter for multi-arch: https://github.com/docker/buildx/pull/1813
          outputs: type=oci,dest=/tmp/radius-docker.tar
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: radius-docker
          path: /tmp/radius-docker.tar

  radius_push:
    name: Push radius Docker image
    # This step is split so that we don't apply "packages: write" permission
    # except when uploading the final Docker image to GHCR.
    runs-on: ubuntu-latest
    if: ( github.ref_type == 'tag' || github.ref == 'refs/heads/master' )
    needs: [radius_build, set_tag_values]
    permissions:
      packages: write

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: radius-docker
          path: /tmp
      - name: Set up ORAS
        uses: oras-project/setup-oras@v1
      # Docker won't directly import OCI images and keep their multi-arch
      # features, but ORAS will: https://oras.land/docs/commands/oras_copy
      - name: Push image to GHCR
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
            oras login -u "${{ github.actor }}" --password-stdin ghcr.io
          oras copy --from-oci-layout "/tmp/radius-docker.tar:devel" \
            "ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/radius:devel"