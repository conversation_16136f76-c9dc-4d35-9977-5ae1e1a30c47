---
name: Javascript Lin<PERSON>
"on":
    - push
    - pull_request
jobs:
  javascript_lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run ESLint to check Javascript files
      run:
        make eslint
  javascript_fmt:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: <PERSON> Prettier to check Javascript files
      run:
        make prettier

