---
name: Container - Kanidm

# This is always built and uploads an OCI image as a build artifact, but only
# pushes to "ghcr.io/kanidm/kanidm:devel" when on "kanidm/kanidm@master".
"on":
  pull_request:
  push:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  set_tag_values:
    runs-on: ubuntu-latest
    name: Set image tag values
    steps:
      - id: step1
        run: |
          echo "OWNER_LC=${OWNER,,}" >> "${GITHUB_OUTPUT}"
        env:
          OWNER: '${{ github.repository_owner }}'
      - id: step2
        run: |
          echo "REF_NAME=$(echo ${REF_NAME,,} | awk -F/ '{print $1}')" >> "${GITHUB_OUTPUT}"
        env:
          REF_NAME: '${{ github.ref_name }}'
    outputs:
      owner_lc: ${{ steps.step1.outputs.OWNER_LC }}
      ref_name: ${{ steps.step2.outputs.REF_NAME }}
  kanidm_build:
    name: Build kanidm Docker image
    runs-on: ubuntu-latest
    needs:
      - set_tag_values
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v5
      - name: Build kanidm
        uses: docker/build-push-action@v6
        with:
          platforms: "linux/amd64"
          tags: ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/kanidm:devel,ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/kanidm:${{ needs.set_tag_values.outputs.ref_name}}

          build-args: |
            "KANIDM_FEATURES="
          file: tools/Dockerfile
          context: .
          labels: ${{ steps.meta.outputs.labels }}
          annotations: ${{ steps.meta.outputs.annotations }}
          # Must use OCI exporter for multi-arch: https://github.com/docker/buildx/pull/1813
          outputs: type=oci,dest=/tmp/kanidm-docker.tar
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: kanidm-docker
          path: /tmp/kanidm-docker.tar

  kanidm_push:
    name: Push kanidm Docker image
    # This step is split so that we don't apply "packages: write" permission
    # except when uploading the final Docker image to GHCR.
    runs-on: ubuntu-latest
    if: ( github.ref_type == 'tag' || github.ref == 'refs/heads/master' )
    needs: [kanidm_build, set_tag_values]
    permissions:
      packages: write

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: kanidm-docker
          path: /tmp
      - name: Set up ORAS
        uses: oras-project/setup-oras@v1
      - name: Push image to GHCR
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
            oras login -u "${{ github.actor }}" --password-stdin ghcr.io
          oras copy --from-oci-layout "/tmp/kanidm-docker.tar:devel" \
            "ghcr.io/${{ needs.set_tag_values.outputs.owner_lc }}/kanidm:devel"