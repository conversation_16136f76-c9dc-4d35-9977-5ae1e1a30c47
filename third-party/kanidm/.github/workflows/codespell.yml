---
name: Spell Check

"on":
  push:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  codespell:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          clean: false

      - name: Install python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install and run codespell
        run: |
          python -m pip install codespell
          make codespell
