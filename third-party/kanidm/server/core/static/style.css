:root {
  --totp-width-and-height: 30px;
  --totp-stroke-width: 60px;
}

html,
body {
  height: 100%;
}

.form-cred-reset-body {
  max-width: 500px;
}

#settings-window .form-cred-reset-body {
  max-width: unset;
}

.form-signin {
  max-width: 680px;
}

/*
* Bootstrap 5.3 fix for input-group validation
* :has checks that a child can be selected with the selector
* + selects the next sibling.
*/
.was-validated .input-group:has(.form-control:invalid) + .invalid-feedback {
  display: block !important;
}

/*
 * Sidebar
 */

.side-menu {
  min-width: 180px;
}

.side-menu-item {
  --icon-size: 24px;
  padding: 0.4rem 0.7rem;
  text-decoration: none;

  &.active {
    font-weight: 600;
  }

  &:hover,
  &.active {
    background-color: var(--bs-tertiary-bg);
  }

  .icon-container img {
    filter: invert(40%);
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

/*
 * Personal Settings sidemenu
 */

/*
 * Navbar
 */
nav.kanidm_navbar {
  background-color: var(--bs-navbar-brand-color);
}

nav a.navbar-brand,
nav a.nav-link {
  color: var(--bs-body-bg);
}

nav a.navbar-brand:hover,
nav a.nav-link:hover {
  color: var(--bs-secondary-bg);
}

/* Bootstrap v5.3.3 fixes for our inverted navbar */
[data-bs-theme="dark"] nav span.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme="light"] nav span.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28222, 222, 222, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

footer {
  background-color: var(--bs-tertiary-bg);
}

.kanidm_logo {
  width: 12em;
  height: 12em;
}

.identity-verification-container {
  display: flex;
  flex-direction: column;
  max-width: fit-content;
  align-items: center;
  margin: auto;
}

.totp-display-container {
  padding: 5px 10px;
  display: flex;
  flex-direction: row;
  max-width: fit-content;
  align-items: center;
  margin: auto;
  border-radius: 15px;
  background-color: #21252915;
  box-shadow:
    -5px -5px 11px #ededed,
    5px 5px 11px #ffffff;
  margin: 15px;
}

.totp-display {
  font-size: 35px;
  margin: 10px;
}

.totp-timer {
  margin: 10px;
  position: relative;
  height: var(--totp-width-and-height);
  width: var(--totp-width-and-height);
}

/* Removes SVG styling that would hide the time label */
.totp-timer__circle {
  fill: none;
  stroke: none;
}

.totp-timer__path-remaining {
  stroke-width: var(--totp-stroke-width);

  /* Makes sure the animation starts at the top of the circle */
  transform: rotate(90deg);
  transform-origin: center;

  /* One second aligns with the speed of the countdown timer */
  transition: 1s linear all;

  stroke: currentColor;
}

.totp-timer__svg {
  transform: scaleX(-1);
}

.totp-timer__path-remaining.green {
  color: var(--bs-success);
}

.totp-timer__path-remaining.orange {
  color: var(--bs-warning);
}

.totp-timer__path-remaining.red {
  color: var(--bs-danger);
}

.totp-timer__path-remaining.no-transition {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}

.card > a {
  height: 150px;
}

.oauth2-img {
  max-width: 100%;
  max-height: 90%;
  padding: 10px;
  height: 100%;
}

.btn-tiny {
  --bs-btn-padding-y: 0.05rem;
  --bs-btn-padding-x: 0.4rem;
  --bs-btn-font-size: 0.75rem;
}

#cred-update-commit-bar {
  display: block;
  /*
  position: fixed;
  bottom: .5rem;
  left: 50%;
  transform: translateX(-50%);
  */
  background: white;
}

.icon-container {
  padding: 2px;
  width: var(--icon-size);
  height: var(--icon-size);
}

.ssh-list-icon {
  --icon-size: 32px;
  width: var(--icon-size);
  height: var(--icon-size);
  transform: rotate(35deg);
}
