[package]
name = "kanidmd_core"
description = "Kanidm Server Core and Library"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
test = true
doctest = false

[features]
default = []
dev-oauth2-device-flow = []

[dependencies]
askama = { workspace = true, features = ["with-axum"] }
askama_axum = { workspace = true }
axum = { workspace = true }
axum-htmx = { workspace = true }
axum-extra = { workspace = true }
axum-macros = { workspace = true }
bytes = { workspace = true }
cidr = { workspace = true, features = ["serde"] }
chrono = { workspace = true }
compact_jwt = { workspace = true }
cron = { workspace = true }
filetime = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }
haproxy-protocol = { workspace = true, features = ["tokio"] }
hashbrown = { workspace = true }
hyper = { workspace = true }
hyper-util = { workspace = true }
kanidm_proto = { workspace = true }
kanidm_utils_users = { workspace = true }
kanidmd_lib = { workspace = true }
kanidm_lib_crypto = { workspace = true }
kanidm_lib_file_permissions = { workspace = true }
ldap3_proto = { workspace = true }
libc = { workspace = true }
openssl = { workspace = true }
opentelemetry = { workspace = true, features = ["logs"] }
qrcode = { workspace = true, features = ["svg"] }
regex = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_with = { workspace = true }
sketching = { workspace = true }
sshkeys = { workspace = true }
sshkey-attest = { workspace = true }
time = { workspace = true, features = ["serde", "std", "local-offset"] }
tokio = { workspace = true, features = ["net", "sync", "io-util", "macros"] }
tokio-openssl = { workspace = true }
tokio-util = { workspace = true, features = ["codec"] }
toml = { workspace = true }
tower = { version = "0.5.2", features = ["tokio-stream", "tracing"] }
tower-http = { version = "0.6.4", features = [
    "compression-gzip",
    "fs",
    "tokio",
    "trace",
    "tracing",
    "uuid",
] }
tracing = { workspace = true, features = ["attributes"] }
url = { workspace = true, features = ["serde"] }
uuid = { workspace = true, features = ["serde", "v4"] }
utoipa = { workspace = true, features = [
    "axum_extras",
    "openapi_extensions",
    "preserve_order",     #  Preserve order of properties when serializing the schema for a component.
    "time",
    "url",
    "uuid",
] }
utoipa-swagger-ui = { workspace = true, features = ["axum"] }

webauthn-rs = { workspace = true, features = [
    "resident-key-support",
    "preview-features",
    "danger-credential-internals",
] }

[dev-dependencies]
walkdir = { workspace = true }
tempfile = { workspace = true }
kanidmd_lib = { workspace = true, features = ["test"] }

[build-dependencies]
kanidm_build_profiles = { workspace = true }


[package.metadata.cargo-machete]
ignored = [
    "opentelemetry",         # feature gated
    "kanidm_build_profiles",
]
