(% extends "base.html" %)

(% block title %)Login(% endblock %)

(% block head %)
(% endblock %)

(% block body %)
<main id="main" class="form-signin m-auto align-items-center d-flex flex-column">
	(% if display_ctx.domain_info.image().is_some() %)
	<img src="/ui/images/domain"
		alt="(( display_ctx.domain_info.display_name() ))" class="kanidm_logo" />
	(% else %)
	<img
		src="/pkg/img/logo-square.svg?v=((crate::https::cache_buster::get_cache_buster_key()))"
		alt="(( display_ctx.domain_info.display_name() ))" class="kanidm_logo" />
	(% endif %)
	<h3>(( display_ctx.domain_info.display_name() ))</h3>
	(% if let Some(reauth) = display_ctx.reauth %)
	<div class="alert alert-info" role="alert">
		Reauthenticating as (( reauth.username )) to access (( reauth.purpose ))
	</div>
	(% else if let Some(oauth2) = display_ctx.oauth2 %)
	<div class="alert alert-info" role="alert">
		Authenticate to access (( oauth2.client_name ))
	</div>
	(% endif %)
	<div>
		(% block logincontainer %)
		(% endblock %)
	</div>
</main>
(% endblock %)
