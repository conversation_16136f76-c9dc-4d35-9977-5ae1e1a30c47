<main class="container-lg">
	<div>
		<h2>Applications</h2>
	</div>
	<hr />
	(% if apps.is_empty() %)
	<p>No linked applications available</p>
	(% else %)
	<div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
		(% for app in apps %)
		<div class="col-md-3">
			<div class="card text-center">
				(% match app %)
				(% when AppLink::Oauth2 with { name, display_name, redirect_url, has_image }
				%)
				<a href="(( redirect_url ))" class="link-emphasis stretched-link mt-2">
					(% if has_image %)
					<img src="/ui/images/oauth2/(( name ))" class="oauth2-img"
						alt="((display_name)) icon" id="(( name ))">
					(% else %)
					<img
						src="/pkg/img/icon-oauth2.svg?v=((crate::https::cache_buster::get_cache_buster_key()))"
						class="oauth2-img" alt="missing-icon icon" id="(( name ))">
					(% endif %)
				</a>
				<label for="(( name ))">(( display_name ))</label>
				(% endmatch %)
			</div>
		</div>
		(% endfor %)
	</div>
	(% endif %)
</main>
