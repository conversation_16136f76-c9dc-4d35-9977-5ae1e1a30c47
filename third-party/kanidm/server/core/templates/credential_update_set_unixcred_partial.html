<div>
    <form class="row g-2 pb-3 needs-validation" id="newPasswordForm" novalidate>
        <input hidden type="text" autocomplete="username" />
        (% let potentially_invalid_input_class = "" %)
        (% let potentially_invalid_reinput_class = "" %)
        (% let pwd_equal = true %)

        (% if let PwdCheckResult::Failure with { pwd_equal, warnings } = check_res %)
            (% let pwd_equal = pwd_equal.clone() %)
            (% if !warnings.is_empty() %)
                (% let potentially_invalid_input_class = "is-invalid" %)
            (% endif %)
            (% if pwd_equal %)
                (% let potentially_invalid_reinput_class = "is-invalid" %)
            (% endif %)
        (% endif %)

        <label for="new-password" class="form-label">Enter New Password</label>
        <input
                aria-describedby="password-validation-feedback"
                autocomplete="new-password"
                class="form-control ((potentially_invalid_input_class))"
                name="new_password"
                id="new-password"
                placeholder=""
                type="password"
                required
                autofocus
        />
        <!-- bootstrap hides the feedback if we remove is-invalid from the input above -->
        (% if let PwdCheckResult::Failure with { pwd_equal, warnings } = check_res %)
        <div id="password-validation-feedback" class="invalid-feedback d-block">
            <ul>
                (% for warn in warnings %)
                    <li>(( warn ))</li>
                (% endfor %)
            </ul>
        </div>
        (% endif %)

        <label for="new-password-check" class="form-label">Repeat Password</label>
        <input
                aria-describedby="neq-password-validation-feedback"
                autocomplete="new-password"
                class="form-control ((potentially_invalid_reinput_class))"
                name="new_password_check"
                id="new-password-check"
                placeholder=""
                type="password"
                required
        />
        <div id="neq-password-validation-feedback" class="invalid-feedback">
            <ul><li>Passwords don't match</li></ul>
        </div>
    </form>
    <div class="g-3 d-flex justify-content-end" hx-target="#credentialUpdateDynamicSection">
        <button id="password-cancel" type="button" class="btn btn-danger me-2" hx-get=((Urls::CredReset)) hx-target="body">Cancel</button>
        <button id="password-submit" type="button" class="btn btn-primary"
                hx-post="/ui/reset/set_unixcred"
                hx-include="#newPasswordForm"
        >Submit</button>
    </div>
</div>

