<hr class="my-4" />
<h4>Passkeys</h4>
<p>Easy to use digital credentials with self-contained multi-factor authentication designed to replace passwords.</p>
<p>
    <a target="_blank" href="https://support.microsoft.com/en-us/windows/passkeys-in-windows-301c8944-5ea2-452b-9886-97e4d2ef4422">Windows</a>,
    <a target="_blank" href="https://support.apple.com/guide/mac-help/create-a-passkey-mchl4af65d1a/mac">MacOS</a>,
    <a target="_blank" href="https://support.google.com/android/answer/14124480?hl=en">Android</a>, and
    <a target="_blank" href="https://support.apple.com/guide/iphone/use-passkeys-to-sign-in-to-apps-and-websites-iphf538ea8d0/ios">iOS</a>
    have built-in support for passkeys.
</p>
<ul class="list-group">
(% for passkey in passkeys %)
    <div class="list-group-item d-flex justify-content-between align-items-center">
        <div>(( passkey.tag ))</div>
        <div>
            <button type="button" class="btn btn-danger btn-tiny" id="(( passkey.tag ))"
                    hx-target="#credentialUpdateDynamicSection"
                    hx-confirm="Are you sure you want to delete passkey (( passkey.tag )) ?"
                    hx-post="/ui/api/remove_passkey" hx-vals='{"uuid": "(( passkey.uuid ))"}'>
                Remove
            </button>
        </div>
    </div>
(% endfor %)
</ul>