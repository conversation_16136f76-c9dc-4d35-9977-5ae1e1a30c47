<div hx-target="#credentialUpdateDynamicSection">
    <hr class="my-4" />
    <h4>Alternative Authentication Methods</h4>

    <p>
        (% match primary_state %)
            (% when CUCredState::Modifiable %)
                If possible, passkeys should be used instead, as they are phishing and exploit resistant.
            (% when CUCredState::DeleteOnly %)
                If possible, passkeys should be used instead, as they are phishing and exploit resistant.
                <br>Account policy prevents you modifying these credentials, but you may remove them.
            (% when CUCredState::AccessDeny %)
                You do not have access to modify these credentials.
            (% when CUCredState::PolicyDeny %)
                Account policy prevents you from setting these credentials
        (% endmatch %)
    </p>

    (% if matches!(primary_state, CUCredState::Modifiable) %)
        <div class="d-flex flex-column row-gap-4">
            (% match primary %)
                (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::Password }) %)
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6><b>Password</b></h6>
                        </div>
                        <div class="flex-shrink-0 ps-3">
                            <button type="button" class="btn btn-sm btn-primary" hx-post="/ui/reset/change_password">
                                Change Password
                            </button>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6><b>Time-based One Time Password (TOTP)</b></h6>
                            <p>TOTPs are 6 digit codes generated on-demand as a second authentication factor.</p>
                        </div>
                        <div class="flex-shrink-0 ps-3">
                            <button type="button" class="btn btn-sm btn-primary" hx-post="/ui/reset/add_totp">
                                Add TOTP
                            </button>
                        </div>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-danger" hx-post="/ui/api/delete_alt_creds" hx-confirm="Delete your Password and any associated alternative authentication methods? NOTE: this will not remove Passkeys.">
                            Delete Alternative Credentials
                        </button>
                    </div>
                (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::PasswordMfa(totp_set, _security_key_labels, _backup_codes_remaining)}) %)
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6><b>Password</b></h6>
                        </div>                    
                        <div class="flex-shrink-0 ps-3">
                            <button type="button" class="btn btn-sm btn-secondary" hx-post="/ui/reset/change_password">
                                Change Password
                            </button>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6><b>Time-based One Time Password (TOTP)</b></h6>
                                <p>TOTPs are 6 digit codes generated on-demand as a second authentication factor.</p>
                            </div>
                            <div class="flex-shrink-0 ps-3">
                                <button type="button" class="btn btn-sm btn-secondary" hx-post="/ui/reset/add_totp">
                                    Add TOTP
                                </button>
                            </div>
                        </div>
                        <p>Registered authenticators:</p>
                        <ul class="list-group">
                            (% for totp in totp_set %)
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between">
                                        <div>(( totp ))</div>
                                        <button type="button" class="btn btn-tiny btn-danger" hx-post="/ui/api/remove_totp" hx-vals='{"name": "(( totp ))"}'>
                                            Remove
                                        </button>
                                    </div>
                                </li>
                            (% endfor %)
                        </ul>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-danger" hx-post="/ui/api/delete_alt_creds" hx-confirm="Delete your Password and any associated MFA?
                        Note: this will not remove Passkeys.">
                            Delete Alternative Credentials
                        </button>
                    </div>
                (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::GeneratedPassword }) %)
                    <div>
                        <h6><b>Password</b></h6>
                        <p>In order to set up alternative authentication methods, you must delete the generated password.</p>
                        <button type="button" class="btn btn-outline-danger" hx-post="/ui/api/delete_alt_creds" >
                            Delete Generated Password
                        </button>
                    </div>
                (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::Passkey(_) }) %)
                    <div>
                        <p>Webauthn Only - Will migrate to passkeys in a future update</p>
                        <button type="button" class="btn btn-outline-danger" hx-post="/ui/api/delete_alt_creds" hx-confirm="Delete your Password and any associated MFA?
                        Note: this will not remove Passkeys.">
                            Delete Alternative Credentials
                        </button>
                    </div>
                (% when None %)
                    <div>
                        <button type="button" class="btn btn-warning" hx-post="/ui/reset/add_password">
                            Add Password
                        </button>
                    </div>
            (% endmatch %)
        </div>
    (% else if matches!(primary_state, CUCredState::DeleteOnly) %)
        <div>
            <button type="button" class="btn btn-warning" hx-post="/ui/api/delete_alt_creds" hx-confirm="Delete your Password and any associated MFA?
            Note: this will not remove Passkeys.">
                Delete Legacy Credentials
            </button>
        </div>
    (% endif %)
</div>
