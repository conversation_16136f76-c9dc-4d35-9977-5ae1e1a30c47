(% extends "base_htmx.html" %)

(% block title %)Reset Credentials(% endblock %)

(% block head %)
<!-- TODO: janky preloading them here because I assumed htmx swapped new scripts in on boosted requests, we can replace navigation to cred update with a full redirect later, and clean this up then -->
<script
    src="/pkg/external/cred_update.js?v=((crate::https::cache_buster::get_cache_buster_key()))"></script>
<script
    src="/pkg/external/base64.js?v=((crate::https::cache_buster::get_cache_buster_key()))"
    async></script>
(% endblock %)

(% block body %)
<main class="flex-shrink-0 container form-signin m-auto" id="cred-reset-form">
    <center>
        (% if domain_info.image().is_some() %)
        <img src="/ui/images/domain"
             alt="(( domain_info.display_name() ))" class="kanidm_logo" />
        (% else %)
        <img
            src="/pkg/img/logo-square.svg?v=((crate::https::cache_buster::get_cache_buster_key()))"
            alt="(( domain_info.display_name() ))" class="kanidm_logo" />
        (% endif %)
        <h2>(( domain_info.display_name() ))</h2>
        <div />
        <h3>Credential Reset</h3>
    </center>
    <form class="mb-3">
        <div>
            <label for="token" class="form-label">Enter your credential reset
                token.</label>
            <input
                id="token"
                name="token"
                autofocus
                aria-describedby="unknown-reset-token-validation-feedback"
                (% if wrong_code %)
                class='form-control is-invalid'
                (% else %)
                class='form-control'
                (% endif %)>
            (% if wrong_code %)
            <div id="unknown-reset-token-validation-feedback"
                class="invalid-feedback">
                <ul><li>Unknown reset token.<br>Brand-new tokens might not be
                        synced yet, <br>wait a few minutes before trying
                        again.</li></ul>
            </div>
            (% endif %)
        </div>
    </form>
    <p class="d-flex flex-row flex-wrap justify-content-between">
        <button class="btn btn-secondary" aria-label="Return home" hx-get="/ui"
            hx-target="body">
            Return to the home page
        </button>
        <button class="btn btn-primary"
            hx-get=""
            hx-include="form"
            hx-target="body"
            type="submit">
            Submit
        </button>
    </p>
</main>
(% endblock %)
