(% extends "user_settings_partial_base.html" %)

(% block selected_setting_group %)
Profile
(% endblock %)

(% block settings_window %)

<form>
    <div class="mb-2 row">
        <label for="profileUserName" class="col-12 col-md-3 col-xl-2 col-form-label">User name</label>
        <div class="col-12 col-md-6 col-lg-5">
            <input type="text" readonly class="form-control-plaintext" id="profileUserName" value="(( account_name ))">
        </div>
    </div>

    <div class="mb-2 row">
        <label for="profileDisplayName" class="col-12 col-md-3 col-xl-2 col-form-label">Display name</label>
        <div class="col-12 col-md-6 col-lg-5">
            <input type="text" class="form-control-plaintext" id="profileDisplayName" value="(( display_name ))" disabled>
        </div>
    </div>
    
    <div class="mb-2 row">
        <label for="profileEmail" class="col-12 col-md-3 col-xl-2 col-form-label">Email</label>
        <div class="col-12 col-md-6 col-lg-5">
            <input type="email" disabled class="form-control-plaintext" id="profileEmail" value="(( email.clone().unwrap_or("None configured".to_string())))">
        </div>
    </div>

    <!-- Edit button -->
    <!-- <div class="pt-4">
        (% if can_rw %)
            <button class="btn btn-primary" type="button" hx-post="/ui/api/user_settings/edit_profile" disabled>Edit (Currently Not Working!)</button>
        (% else %)
            <a href=(Urls::ProfileUnlock) hx-boost="false">
                <button class="btn btn-primary" type="button">((UiMessage::UnlockEdit))</button>
            </a>
        (% endif %)
    </div> -->
</form>

(% endblock %)

