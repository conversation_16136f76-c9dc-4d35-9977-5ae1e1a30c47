<hr>
<div class="d-flex flex-column row-gap-4">
    <h4>Add new SSH Key</h4>
    <form class="row-gap-3 d-flex flex-column needs-validation"
        hx-target="#credentialUpdateDynamicSection"
        hx-post="/ui/reset/add_ssh_publickey">
        <div>
            <label for="key-title" class="form-label">Title</label>
            <input type="text"
                class="form-control(% if let Some(_) = title_error %) is-invalid(% endif %)"
                id="key-title" name="title"
                aria-describedby="title-validation-feedback"
                autocapitalize="off" autocomplete="off" required
                value="(% if let Some(key_title) = key_title %)(( key_title ))(% endif %)">
            (% if let Some(title_error) = title_error %)
            <div id="title-validation-feedback" class="invalid-feedback">
                (( title_error ))
            </div>
            (% endif %)
        </div>
        <div>
            <label for="key-content" class="form-label">Key</label>
            <textarea
                class="form-control(% if let Some(_) = key_error %) is-invalid(% endif %)"
                id="key-content" rows="5" name="key"
                aria-describedby="key-validation-feedback"
                autocapitalize="off" autocomplete="off" required
                placeholder="Begins with 'ssh-rsa', 'ecdsa-sha2-nistp256', 'ecdsa-sha2-nistp384', 'ecdsa-sha2-nistp521', 'ssh-ed25519', '<EMAIL>', or '<EMAIL>'">(% if let Some(key_value) = key_value %)(( key_value ))(% endif %)</textarea>
            (% if let Some(key_error) = key_error %)
            <div id="key-validation-feedback" class="invalid-feedback">
                (( key_error ))
            </div>
            (% endif%)
        </div>

        <div class="column-gap-2 d-flex justify-content-end mt-2"
            hx-target="#credentialUpdateDynamicSection">
            <button type="button" class="btn btn-danger"
                hx-get=((Urls::CredReset)) hx-target="body">Cancel</button>
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
    </form>
</div>
