<hr class="my-4" />
<h4>Attested Passkeys</h4>
<p>Passkeys originating from a signed authenticator.</p>
<ul class="list-group">
    (% for passkey in attested_passkeys %)
    <li class="list-group-item">
        <div class="d-flex justify-content-between">
            <div>(( passkey.tag ))</div>
            <div>
                <button type="button" class="btn btn-danger btn-sml" id="(( passkey.tag ))"
                    hx-target="#credentialUpdateDynamicSection"
                    hx-confirm="Are you sure you want to delete attested passkey (( passkey.tag )) ?"
                    hx-post="/ui/api/remove_passkey" hx-vals='{"uuid": "(( passkey.uuid ))"}'>
                    Remove
                </button>
            </div>
        </div>
    </li>
    (% endfor %)
</ul>