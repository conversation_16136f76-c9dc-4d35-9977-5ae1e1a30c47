(% extends "admin/admin_partial_base.html" %)

(% block persons_item_extra_classes %)active(% endblock %)

(% block admin_page %)
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">Person Management</li>
    </ol>
</nav>

<ul class="list-group">
    (% for (person, _) in persons %)
    <li class="list-group-item d-flex flex-row justify-content-between">
        <div class="d-flex align-items-center">
            <a href="/ui/admin/person/(( person.uuid ))/view" hx-target="#main">(( person.name ))</a> <span class="text-secondary d-none d-lg-inline-block mx-4">(( person.uuid ))</span>
        </div>
        <div class="buttons float-end">
        </div>
    </li>
    (% endfor %)
</ul>
(% endblock %)
