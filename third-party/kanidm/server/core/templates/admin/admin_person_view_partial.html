(% extends "admin/admin_partial_base.html" %)

(% block persons_item_extra_classes %)active(% endblock %)

(% block admin_page %)
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/ui/admin/persons" hx-target="#main">persons Management</a></li>
        <li class="breadcrumb-item active" aria-current="page">Viewing</li>
    </ol>
</nav>

(% include "admin_person_details_partial.html" %)

<hr>

(% if scim_effective_access.search.check(Attribute::Mail|as_ref) %)
<label class="mt-3 fw-bold">Emails</label>
<form hx-validate="true" hx-ext="bs-validation">
    (% if person.mails.len() == 0 %)
    <p>There are no email addresses associated with this person.</p>
    (% else %)
    <ol class="list-group col-12 col-md-8 col-lg-6">
        (% for mail in person.mails %)
        <li id="personMail(( loop.index ))" class="list-group-item d-flex flex-row justify-content-between">
            <div class="d-flex align-items-center">(( mail.value ))</div>
            <div class="buttons float-end">
            </div>
        </li>
        (% endfor %)
    </ol>
    (% endif %)
</form>
(% endif %)

(% if scim_effective_access.search.check(Attribute::DirectMemberOf|as_ref) %)
<label class="mt-3 fw-bold">DirectMemberOf</label>
<form hx-validate="true" hx-ext="bs-validation">
    (% if person.groups.len() == 0 %)
    <p>There are no groups this person is a direct member of.</p>
    (% else %)
    <ol class="list-group col-12 col-md-8 col-lg-6">
        (% for group in person.groups %)
        <li id="personGroup(( loop.index ))" class="list-group-item d-flex flex-row justify-content-between">
            <div class="d-flex align-items-center">(( group.value ))</div>
            <div class="buttons float-end">
            </div>
        </li>
        (% endfor %)
    </ol>
    (% endif %)
</form>
(% endif %)



(% endblock %)