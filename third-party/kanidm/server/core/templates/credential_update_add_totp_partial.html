<div>
    <div id="totpInfo">
        (% if let Some(TotpInit with { secret, qr_code_svg, steps, digits, algo, uri }) = totp_init %)
            <div>((qr_code_svg|safe))</div>
            <code>((uri|safe))</code>

            <h3>TOTP details</h3>
            <ul>
                <li>Secret: (( secret ))</li>
                <li>Algorithm: (( algo ))</li>
                <li>Time Steps: (( steps )) sec</li>
                <li>Code size: (( digits )) digits</li>
            </ul>
        (% endif %)
    </div>

    <div id="newTotpForm">
        <form class="row g-2 pb-3 needs-validation" novalidate>
            <label for="new-totp-name" class="form-label">Enter a name for your TOTP</label>
            <input
                    aria-describedby="totp-name-validation-feedback"
                    class="form-control (%- if let Some(_) = check.taken_name -%)is-invalid(%- endif -%)
                           (%- if check.bad_name -%)is-invalid(%- endif -%)"
                    name="name"
                    id="new-totp-name"
                    value="(( totp_name ))"
                    required
                    autofocus
            />

            <label for="new-totp-check" class="form-label">Enter a TOTP code to confirm it's working</label>
            <input
                    aria-describedby="new-totp-check-feedback"
                    class="form-control (%- if check.broken_app || check.wrong_code -%)is-invalid(%- endif -%)"
                    name="checkTOTPCode"
                    id="new-totp-check"
                    value="(( totp_value ))"
                    type="text"
                    inputmode="numeric"
                    required
            />

            (% if check.broken_app %)
                <div id="neq-totp-validation-feedback">
                    <ul>
                    <li>Your authenticator appears to be implemented in a way that uses SHA1, rather than SHA256. Are you sure you want to proceed? If you want to try with a new authenticator, enter a new code.</li>
                    </ul>
                </div>
            (% else if check.wrong_code %)
                <div id="neq-totp-validation-feedback">
                    <ul>
                        <li>Incorrect TOTP code - Please try again</li>
                    </ul>
                </div>
            (% else if check.bad_name %)
                <div id="neq-totp-validation-feedback">
                    <ul>
                        <li>The name you provided was empty or blank. Please provide a proper name</li>
                    </ul>
                </div>
            (% else if let Some(name) = check.taken_name %)
                <div id="neq-totp-validation-feedback">
                    <ul>
                        <li>The name "((name))" is either invalid or already taken, Please pick a different one</li>
                    </ul>
                </div>
            (% endif %)

        </form>
        <div class="g-3 d-flex justify-content-end" hx-target="#credentialUpdateDynamicSection">
            <button id="totp-cancel" type="button" class="btn btn-danger me-2" hx-post="/ui/api/cancel_mfareg">Cancel</button>
            (% if check.broken_app %)
                <button id="totp-submit" type="button" class="btn btn-warning"
                        hx-post="/ui/api/add_totp"
                        hx-target="#newTotpForm"
                        hx-select="#newTotpForm > *"
                        hx-vals='{"ignoreBrokenApp": true}'
                        hx-include="#newTotpForm"
                >Accept SHA1</button>
            (% else %)
                <button id="totp-submit" type="button" class="btn btn-primary"
                        hx-post="/ui/api/add_totp"
                        hx-target="#newTotpForm"
                        hx-select="#newTotpForm > *"
                        hx-vals='{"ignoreBrokenApp": false}'
                        hx-include="#newTotpForm"
                >Add</button>
            (% endif %)
        </div>
    </div>
</div>

