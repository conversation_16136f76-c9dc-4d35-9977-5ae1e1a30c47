(% extends "user_settings_partial_base.html" %)

(% block selected_setting_group %)
Unix/Posix Settings
(% endblock %)

(% block settings_window %)
    <fieldset>
        <legend>POSIX Settings</legend>
        <label for="posix-pwd-input">Posix Password</label>
        <input type="text" id="posix-pwd-input" name="posix-pwd" disabled value="*********">
    </fieldset>
    (% if can_rw %)
        <button class="btn btn-primary" type="button" hx-post="/ui/api/user_settings/edit_posix">Edit</button>
    (% else %)
        <a href=(Urls::ProfileUnlock) hx-boost="false">
            <button class="btn btn-primary" type="button">((UiMessage::UnlockEdit))</button>
        </a>
    (% endif %)
(% endblock %)

