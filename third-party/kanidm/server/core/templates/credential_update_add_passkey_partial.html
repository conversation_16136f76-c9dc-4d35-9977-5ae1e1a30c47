<div>
    <div class="row" id="staticPasskeyCreateRow">
        <script id="data">(( challenge|safe ))</script>

        <!-- <PERSON><PERSON> requires a human input to start passkey creation -->
        <div id="passkeyNamingSafariPre">
            <button id="passkeyNamingSafariBtn" class="btn btn-primary">Begin Passkey Enrolment</button>
            <div class="d-flex justify-content-end pt-3 g-3" hx-target="#credentialUpdateDynamicSection">
               <button id="password-cancel" type="button" class="btn btn-danger" hx-post="/ui/api/cancel_mfareg">Cancel</button>
            </div>
        </div>

        <form id="passkeyNamingForm" class="g-2 d-none">
            <b>Adding a new passkey</b>
            <label for="passkey-label" class="form-label">Please name this Passkey</label>
            <input type="text" name="name" id="passkey-label" autofocus required class="form-control">

            <!-- Hidden inputs to put info passkey into form submission -->
            <input hidden type="text" name="creationData" id="passkey-create-data">

            <div class="d-flex justify-content-end pt-3 g-3" hx-target="#credentialUpdateDynamicSection">
                <button id="password-cancel" type="button" class="btn btn-danger" hx-post="/ui/api/cancel_mfareg">Cancel</button>
                <button
                        type="submit"
                        hx-post="/ui/api/finish_passkey"
                        hx-vals='{"class": "(( class ))"}'
                        id="passkeyNamingSubmitBtn"
                        class="btn btn-primary d-none ms-2" disabled
                >Add</button>
            </div>
        </form>
    </div>
</div>
