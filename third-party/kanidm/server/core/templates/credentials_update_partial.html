<script type="module"
    src="/pkg/modules/cred_update.mjs?v=((crate::https::cache_buster::get_cache_buster_key()))"
    async></script>
<script
    src="/pkg/external/base64.js?v=((crate::https::cache_buster::get_cache_buster_key()))"
    async></script>

<div id="credentialUpdateDynamicSection"
    hx-on::before-swap="stillSwapFailureResponse(event)">
    <form class="needs-validation mb-5 pb-5" novalidate>
        (% match ext_cred_portal %)
        (% when CUExtPortal::None %)
        (% when CUExtPortal::Hidden %)
        <p>This account is externally managed. Some features may not be
            available.</p>
        <hr class="my-4" />
        (% when CUExtPortal::Some(url) %)
        <p>This account is externally managed. Some features may not be
            available.</p>
        <a href="(( url ))">Visit the external account portal</a>
        <hr class="my-4" />
        (% endmatch %)

        (% if warnings.len() > 0 %)
        (% for warning in warnings %)
        (% let is_danger = [CURegWarning::WebauthnAttestationUnsatisfiable,
        CURegWarning::Unsatisfiable].contains(warning) %)
        (% if is_danger %)
        <div class='alert alert-danger' role="alert">
            (% else %)
            <div class='alert alert-warning' role="alert">
                (% endif %)

                (% match warning %)
                (% when CURegWarning::MfaRequired %)
                Multi-Factor Authentication is required for your account. Delete
                the generated password and set up either a passkey (recommended)
                or password and two-factor authentication (TOTP) to save
                changes.
                (% when CURegWarning::PasskeyRequired %)
                Passkeys are required for your account.
                (% when CURegWarning::AttestedPasskeyRequired %)
                Attested Passkeys are required for your account.
                (% when CURegWarning::AttestedResidentKeyRequired %)
                Attested Resident Keys are required for your account.
                (% when CURegWarning::WebauthnAttestationUnsatisfiable %)
                A webauthn attestation policy conflict has occurred and you will
                not be able to save your credentials.
                (% when CURegWarning::Unsatisfiable %)
                An account policy conflict has occurred and you will not be able
                to save your credentials.
                (% when CURegWarning::WebauthnUserVerificationRequired %)
                The passkey you attempted to register did not provide user verification. Please
                ensure that you have a PIN or alternative configured on your authenticator.
                (% endmatch %)

                (% if is_danger %)
                <br><br>
                <b>Contact support IMMEDIATELY.</b>
                (% endif %)
            </div>
            (% endfor %)
            <hr class="my-4" />
            (% endif %)

            <!-- Attested Passkeys -->
            (% match attested_passkeys_state %)
            (% when CUCredState::Modifiable %)
            (% include "credentials_update_attested_passkeys.html" %)
            <button type="button" class="btn btn-primary"
                hx-post="/ui/reset/add_passkey" hx-vals='{"class": "Attested"}'
                hx-target="#credentialUpdateDynamicSection">
                Add Attested Passkey
            </button>
            (% when CUCredState::DeleteOnly %)
            (% if attested_passkeys.len() > 0 %)
            (% include "credentials_update_attested_passkeys.html" %)
            (% endif %)
            (% when CUCredState::AccessDeny %)
            (% when CUCredState::PolicyDeny %)
            (% endmatch %)

            <!-- Passkeys -->
            (% match passkeys_state %)
            (% when CUCredState::Modifiable %)
            (% include "credentials_update_passkeys.html" %)
            <!-- Here we are modifiable so we can render the button to add passkeys  -->
            <div class="mt-3">
                <button type="button" class="btn btn-primary"
                    hx-post="/ui/reset/add_passkey"
                    hx-vals='{"class": "Any"}'
                    hx-target="#credentialUpdateDynamicSection">
                    Add Passkey
                </button>
            </div>

            (% when CUCredState::DeleteOnly %)
            (% if passkeys.len() > 0 %)
            (% include "credentials_update_passkeys.html" %)
            (% endif %)
            (% when CUCredState::AccessDeny %)
            (% when CUCredState::PolicyDeny %)
            (% endmatch %)

            <!-- Password, totp credentials -->
            (% let primary_state = primary_state %)
            (% include "credentials_update_primary.html" %)

            (% match unixcred_state %)
            (% when CUCredState::Modifiable %)
            <hr class="my-4" />
            <h4>UNIX Password</h4>
            <p>This password is used when authenticating to a UNIX-like system</p>
            <button type="button" class="btn btn-primary"
                hx-post="/ui/reset/set_unixcred"
                hx-target="#credentialUpdateDynamicSection">
                Set UNIX Password
            </button>
            (% match unixcred %)
            (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::Password }) %)
            <button type="button" class="btn btn-outline-danger"
                hx-post="/ui/api/delete_unixcred"
                hx-target="#credentialUpdateDynamicSection">
                Delete UNIX Password
            </button>
            (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::GeneratedPassword }) %)
            (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::Passkey(_) }) %)
            (% when Some(CredentialDetail { uuid, type_: kanidm_proto::internal::CredentialDetailType::PasswordMfa(_totp_set, _security_key_labels, _backup_codes_remaining)}) %)
            (% when None %)
            (% endmatch %)
            <!-- (% if matches!(primary_state, CUCredState::Modifiable) %)

            (% endif %) -->
            (% when CUCredState::DeleteOnly %)
            (% when CUCredState::AccessDeny %)
            (% when CUCredState::PolicyDeny %)
            (% endmatch %)

            (% match sshkeys_state %)
            (% when CUCredState::Modifiable %)
            <hr class="my-4" />
            <h4>SSH Keys</h4>
            (% if sshkeys.len() > 0 %)
            <p>This is a list of SSH keys associated with your account.</p>
            <ul class="list-group">
                (% for (keyname, sshkey) in sshkeys %)
                <li class="list-group-item d-flex column-gap-3 py-3">
                    <div>
                        <img class="ssh-list-icon" src="/pkg/img/icons/key.svg" alt="" />
                    </div>
                    <div class="d-flex flex-column row-gap-2 flex-grow-1">
                        <div class="d-flex justify-content-between">
                            <div class="fw-bold column-gap-2">
                                (( keyname ))<span class="badge rounded-pill text-bg-dark ms-2">(( sshkey.key_type.short_name ))</span>
                            </div>
                            <button class="btn btn-tiny btn-danger"
                                hx-post="/ui/api/remove_ssh_publickey"
                                hx-vals='{"name": "(( keyname ))"}'
                                hx-target="#credentialUpdateDynamicSection">
                                Remove
                            </button>
                        </div>
                        <div><span class="font-monospace text-break">SHA256:(( sshkey.key ))</span></div>
                        (% if let Some(comment) = sshkey.comment %)
                            <div class="rounded bg-body-tertiary border border-light-subtle text-body-secondary px-2 py-1 align-self-stretch">Comment: (( comment ))</div>
                        (% endif %)
                    </div>
                </li>
                (% endfor %)
            </ul>
            (% else %)
            <p>There are no SSH keys associated with your account.</p>
            (% endif %)
            <div class="mt-3">
                <button class="btn btn-primary" type="button" 
                    hx-post="/ui/reset/add_ssh_publickey"
                    hx-target="#credentialUpdateDynamicSection">
                    Add SSH Key
                </button>
            </div>
            
            (% when CUCredState::DeleteOnly %)
            (% when CUCredState::AccessDeny %)
            (% when CUCredState::PolicyDeny %)
            (% endmatch %)


            <hr class="my-4" />
            <div id="cred-update-commit-bar" class="toast bs-emphasis-color bs-secondary-bg">
                <div class="toast-body">
                    <span class="d-flex align-items-center">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                height="16" fill="currentColor"
                                class="bi bi-floppy2-fill" viewBox="0 0 16 16">
                                <path d="M12 2h-2v3h2z" />
                                <path
                                    d="M1.5 0A1.5 1.5 0 0 0 0 1.5v13A1.5 1.5 0 0 0 1.5 16h13a1.5 1.5 0 0 0 1.5-1.5V2.914a1.5 1.5 0 0 0-.44-1.06L14.147.439A1.5 1.5 0 0 0 13.086 0zM4 6a1 1 0 0 1-1-1V1h10v4a1 1 0 0 1-1 1zM3 9h10a1 1 0 0 1 1 1v5H2v-5a1 1 0 0 1 1-1" />
                            </svg>
                            <b>Careful</b> - Unsaved changes will be lost</div>
                    </span>
                    <div class="mt-3 d-flex column-gap-2">
                        <button class="btn btn-danger"
                            hx-post="/ui/api/cu_cancel"
                            hx-boost="false"
                        >Discard Changes</button>
                        <span class="d-inline-block" tabindex="0"
                            data-bs-toggle="tooltip"
                            data-bs-title="Unresolved warnings">
                            <button
                                class="btn btn-success"
                                type="submit"
                                hx-post="/ui/api/cu_commit"
                                hx-boost="false"
                                (% if !can_commit %)disabled(% endif
                                %)>Save Changes</button>
                        </span>
                    </div>
                </div>
            </div>
        </form>
    </div>
