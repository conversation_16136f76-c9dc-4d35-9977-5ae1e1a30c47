version = "2"
bindaddress = "[::]:8443"
ldapbindaddress = "127.0.0.1:3636"

db_fs_type = "zfs"
db_path = "/tmp/kanidm/kanidm.db"
tls_chain = "/tmp/kanidm/chain.pem"
tls_key = "/tmp/kanidm/key.pem"
# tls_client_ca = "/tmp/kanidm/client_ca"

#   The log level of the server. May be one of info, debug, trace
#
#   NOTE: this is overridden by KANIDM_LOG_LEVEL environment variable
#   Defaults to "info"
#
# log_level = "info"
# log_level = "debug"
log_level = "debug"

# otel_grpc_url = "http://localhost:4317"

domain = "localhost"
origin = "https://localhost:8443"

[online_backup]
# defaults to db_path
# path = "/tmp/kanidm/backups/"
schedule = "@hourly"
# enabled = true # default enabled
