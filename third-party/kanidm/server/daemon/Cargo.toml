[package]
name = "daemon"
description = "Kanidm Server Daemon"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "kanidmd"
path = "src/main.rs"
test = false
doctest = false

[features]
dhat-heap = ["dep:dhat"]
dhat-ad-hoc = ["dep:dhat"]

[dependencies]
kanidm_proto = { workspace = true }
kanidmd_core = { workspace = true }
kanidm_lib_file_permissions = { workspace = true }
sketching = { workspace = true }
fs4 = { workspace = true }
futures = { workspace = true }

dhat = { workspace = true, optional = true }
clap = { workspace = true, features = ["env"] }
reqwest = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread", "macros", "signal"] }
tokio-util = { workspace = true, features = ["codec"] }
tracing = { workspace = true }
serde_json = { workspace = true }

[target.'cfg(target_os = "linux")'.dependencies]
sd-notify = { workspace = true }
prctl = { workspace = true }

[target.'cfg(target_family = "windows")'.dependencies]
whoami = { workspace = true }

[target.'cfg(not(target_family = "windows"))'.dependencies]
kanidm_utils_users = { workspace = true }

[target.'cfg(not(target_os = "illumos"))'.dependencies]
mimalloc = { workspace = true }

[build-dependencies]
clap = { workspace = true, features = ["derive"] }
clap_complete = { workspace = true }
kanidm_build_profiles = { workspace = true }

## Debian packaging
[package.metadata.deb]
name = "kanidmd"
maintainer = "James Hodgkinson <<EMAIL>>"
# Can't use $auto depends because the name of libssl3 varies by distro and version
depends = [
	"libc6",
	"tpm-udev",
	"libssl3 | libssl3t64",
]
section = "network"
priority = "optional"
changelog = "../../target/debian/changelog"  # Generated by platform/debian/build_debs.sh
assets = [
    [ "target/release/kanidmd", "usr/bin/", "755" ],
  	[ "debian/group.conf", "usr/lib/sysusers.d/kanidmd.conf", "644" ],
    [ "debian/server.toml", "etc/kanidmd/server.toml", "640" ],
    [ "../../examples/server.toml", "usr/share/kanidmd/", "444" ],
    [ "../core/static/**/*", "usr/share/kanidmd/static", "444" ],
]
maintainer-scripts = "debian/"
systemd-units = [
	{ unit-name = "kanidmd", enable = false},  # Cannot start without manual config
]


[package.metadata.cargo-machete]
ignored = ["clap_complete", "kanidm_build_profiles"]
