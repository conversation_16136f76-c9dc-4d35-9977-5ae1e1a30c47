#!/bin/sh
# postinst script for kanidmd
#
# see: dh_installdeb(1)

set -e


case "$1" in
    configure)
        echo "Creating the kanidmd group for config & cert ownership..."
        systemd-sysusers
        echo "Fixing ownership of server configuration ..."
        chown :kanidmd /etc/kanidmd/server.toml*

        echo "============================="
        echo "Thanks for installing Kanidm!"
        echo "============================="
        echo "Please ensure you modify the configuration file at /etc/kanidmd/server.toml"
        echo "Only then: systemctl enable kanidmd.service"
        echo "Full examples are in /usr/share/kanidmd/"
    ;;

    abort-upgrade|abort-remove|abort-deconfigure)
    ;;

    *)
        echo "postinst called with unknown argument \`$1'" >&2
        exit 1
    ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
