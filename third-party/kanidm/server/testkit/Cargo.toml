[package]
name = "kanidmd_testkit"
description = "Kanidm Server Test Framework"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
name = "kanidmd_testkit"
path = "src/lib.rs"
test = false
doctest = false

[features]
# default = ["dev-oauth2-device-flow"]
# Enables webdriver tests, you need to be running a webdriver server
webdriver = []

dev-oauth2-device-flow = []

[dependencies]
kanidm_client = { workspace = true }
kanidm_proto = { workspace = true }
kanidmd_core = { workspace = true }
kanidmd_lib = { workspace = true }
openssl = { workspace = true }
reqwest = { workspace = true, default-features = false, features = ["cookies"] }
serde = { workspace = true }
sketching = { workspace = true }
testkit-macros = { workspace = true }
tokio = { workspace = true, features = [
    "net",
    "sync",
    "io-util",
    "macros",
    "rt",
] }
tracing = { workspace = true, features = ["attributes"] }
url = { workspace = true, features = ["serde"] }

[build-dependencies]
kanidm_build_profiles = { workspace = true }

[dev-dependencies]
cidr = { workspace = true }
compact_jwt = { workspace = true }
escargot = "0.5.13"
# used for webdriver testing
fantoccini = { version = "0.21.5" }
futures = { workspace = true }
hex = { workspace = true }
hyper = { workspace = true }
http-body-util = { workspace = true }
hyper-util = { workspace = true }
ldap3_client = { workspace = true }
oauth2_ext = { workspace = true, default-features = false, features = [
    "reqwest",
] }
petgraph = { version = "0.8.1", features = ["serde"] }
serde_json = { workspace = true }
time = { workspace = true }
tokio-openssl = { workspace = true }
kanidm_lib_crypto = { workspace = true }
uuid = { workspace = true }
webauthn-authenticator-rs = { workspace = true }
jsonschema = { workspace = true }

[package.metadata.cargo-machete]
ignored = ["escargot", "futures", "kanidm_build_profiles"]
