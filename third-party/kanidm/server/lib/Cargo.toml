[package]
name = "kanidmd_lib"
description = "Kanidm Server Backend Library"
documentation = "https://docs.rs/kanidm/latest/kanidm/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
name = "kanidmd_lib"
path = "src/lib.rs"
test = true
doctest = false

[features]
default = []
dhat-heap = ["dep:dhat"]
dhat-ad-hoc = ["dep:dhat"]
dev-oauth2-device-flow = [] # still-in-development oauth2 device flow support
test = []                   # Enable this for cross-package test features.

[dependencies]
base64 = { workspace = true }
base64urlsafedata = { workspace = true }
bitflags = { workspace = true }
compact_jwt = { workspace = true, features = ["openssl", "hsm-crypto"] }
concread = { workspace = true }
dhat = { workspace = true, optional = true }
dyn-clone = { workspace = true }
# futures-util = { workspace = true }
hashbrown = { workspace = true }
idlset = { workspace = true }
itertools = { workspace = true }
kanidm_proto = { workspace = true }
kanidm_lib_crypto = { workspace = true }
lazy_static = { workspace = true }
ldap3_proto = { workspace = true }
# libsqlite3-sys = { workspace = true }
num_enum = { workspace = true }
# We need to explicitly ask for openssl-sys so that we get the version propagated
# into the build.rs for legacy feature checks.
openssl-sys = { workspace = true }
openssl = { workspace = true }
rand = { workspace = true }
regex = { workspace = true, features = [
    "std",
    "perf",
    "perf-inline",
    "unicode",
    "unicode-gencat",
] }
rusqlite = { workspace = true, features = ["array", "bundled"] }

serde = { workspace = true, features = ["derive"] }
serde_cbor = { workspace = true }
serde_json = { workspace = true }
sketching = { workspace = true }
smolset = { workspace = true }
sshkey-attest = { workspace = true }
time = { workspace = true, features = ["serde", "std"] }
tokio = { workspace = true, features = ["net", "sync", "time", "rt"] }
nonempty = { workspace = true, features = ["serialize"] }

tracing = { workspace = true, features = ["attributes"] }
url = { workspace = true, features = ["serde"] }
utoipa = { workspace = true }
uuid = { workspace = true, features = ["serde", "v4"] }
webauthn-rs = { workspace = true, features = [
    "resident-key-support",
    "preview-features",
    "danger-credential-internals",
] }
webauthn-rs-core = { workspace = true }
zxcvbn = { workspace = true }
serde_with = { workspace = true, features = ["time_0_3", "base64"] }
hex = { workspace = true }
lodepng = { workspace = true }
image = { workspace = true, default-features = false, features = [
    "gif",
    "jpeg",
    "webp",
] }
svg = { workspace = true }

# because windows really can't build without the bundled one
[target.'cfg(target_family = "windows")'.dependencies]
whoami = { workspace = true }

[dev-dependencies]
compact_jwt = { workspace = true, features = [
    "openssl",
    "hsm-crypto",
    "unsafe_release_without_verify",
] }
futures = { workspace = true }
kanidmd_lib_macros = { workspace = true }
# This is needed so that we can use a test feature across the crate boundary to proto
# so we can enable certain hidden test only attributes.
kanidm_proto = { workspace = true, features = ["test"] }
webauthn-authenticator-rs = { workspace = true }


[target.'cfg(not(target_os = "illumos"))'.dev-dependencies]
mimalloc = { workspace = true }

[build-dependencies]
hashbrown = { workspace = true }
kanidm_build_profiles = { workspace = true }
regex = { workspace = true }

[package.metadata.cargo-machete]
ignored = [
    "openssl-sys", # see note above
    "whoami",      # used in windows
]
