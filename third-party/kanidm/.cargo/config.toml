# The default ld from glibc is impossibly slow and consumes huge amounts of
# memory. We use lld by default which often is twice as fast for significantly
# less ram consumption.

[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=lld", "-Ctarget-cpu=native", "-C", "force-frame-pointers=yes"]

[target.aarch64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=lld", "-Ctarget-cpu=native", "-C", "force-frame-pointers=yes"]
