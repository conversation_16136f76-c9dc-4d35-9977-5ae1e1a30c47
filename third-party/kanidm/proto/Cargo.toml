[package]
name = "kanidm_proto"
description = "Kanidm Protocol Bindings for serde"
documentation = "https://docs.rs/kanidm_proto/latest/kanidm_proto/"

version = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[lib]
test = true
doctest = true

[features]
default = []
test = []
dev-oauth2-device-flow = []

[dependencies]
base32 = { workspace = true }
base64 = { workspace = true }
clap = { workspace = true }
num_enum = { workspace = true }
scim_proto = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_with = { workspace = true, features = ["time_0_3", "base64", "hex"] }
smartstring = { workspace = true, features = ["serde"] }
time = { workspace = true, features = ["serde", "std"] }
url = { workspace = true, features = ["serde"] }
urlencoding = { workspace = true }
utoipa = { workspace = true }
uuid = { workspace = true, features = ["serde"] }
webauthn-rs-proto = { workspace = true }
sshkey-attest = { workspace = true }
sshkeys = { workspace = true }

[dev-dependencies]
enum-iterator = { workspace = true }
serde_urlencoded = { workspace = true }

[build-dependencies]
kanidm_build_profiles = { workspace = true }
