# this is a template file to set up an oauth2 RP for testing
# LoadModule auth_openidc_module  /usr/lib/apache2/modules/mod_auth_openidc.so
LogLevel debug
ErrorLog /dev/stderr
TransferLog /dev/stdout
<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName localhost

     SSLEngine on
     # use the results from insecure_generate_certs.sh - super handy
     SSLCertificateFile    /certs/cert.pem
     SSLCertificateKeyFile /certs/key.pem

# since we're using a self-signed cert we have to disable this
OIDCSSLValidateServer Off
OIDCProviderMetadataURL    https://${KANIDM_HOSTNAME}:${KANIDM_PORT}/oauth2/openid/test_rp/.well-known/openid-configuration
OIDCClientID               test_rp
OIDCClientSecret           ${KANIDM_CLIENT_SECRET}
OIDCUserInfoTokenMethod    authz_header
OIDCPKCEMethod             S256
OIDCCookieSameSite         On

# Define the OpenID Connect scope that is requested from the OP (eg. "openid email profile").
# When not defined, the bare minimal scope "openid" is used.
# OIDCScope                  "openid"
# OIDCRedirectURI is a vanity URL that must point to a path protected by this module but must NOT point to any content
OIDCRedirectURI            https://${OAUTH_HOSTNAME}/redirect_url
OIDCCryptoPassphrase       Th1sIsA5uperS3cretP4ssphraseD0ntT3llTh3Cr4bz

OIDCUserInfoRefreshInterval 300
OIDCJWKSRefreshInterval 300

OIDCSessionInactivityTimeout 300
OIDCSessionType client-cookie:persistent

# preferred_username
OIDCRemoteUserClaim preferred_username

<Location />
    AuthType openid-connect
    Require valid-user
</Location>

</VirtualHost>
</IfModule>
