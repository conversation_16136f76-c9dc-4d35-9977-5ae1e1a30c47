# The sync account token as generated by "system sync generate-token".
sync_token = "eyJhb..."

# A cron-like expression of when to run when in scheduled mode. The format is:
#   sec  min   hour   day of month   month   day of week   year
#
# The default of this value is "0 */5 * * * * *" which means "run every 5 minutes".
# schedule = ""

# If you want to monitor the status of the scheduled sync tool (you should)
# then you can set a bind address here.
#
# If not set, defaults to no status listener.
# status_bind = ""

# The LDAP URI to FreeIPA. This MUST be LDAPS. You should connect to a unique single
# server in the IPA topology rather than via a load balancer or dns srv records. This
# is to prevent replication conflicts and issues due to how 389-ds content sync works.
ipa_uri = "ldaps://specific-server.ipa.dev.kanidm.com"
# Path to the IPA CA certificate in PEM format. This can be found on an IPA server
# in the file `/etc/ipa/ca.crt`
ipa_ca = "/path/to/ipa-ca.pem"
# The DN of an account with content sync rights. By default cn=Directory Manager has
# this access.
ipa_sync_dn = "cn=Directory Manager"
ipa_sync_pw = "directory manager password"
# The basedn to examine.
ipa_sync_base_dn = "dc=ipa,dc=dev,dc=kanidm,dc=com"

# By default Kanidm separates the primary account password and credentials from
# the unix credential. This allows the unix password to be isolated from the
# account password so that compromise of one doesn't compromise the other. However
# this can be surprising for new users during a migration. This boolean allows the
# user password to be set as the unix password during the migration for consistency
# and then after the migration they are "unlinked".
#
# sync_password_as_unix_password = false

# The sync tool can alter or exclude entries. These are mapped by their syncuuid
# (not their ipa-object-uuid). The syncuuid is derived from nsUniqueId in 389-ds.
# This is chosen oven DN because DN's can change with modrdn where nsUniqueId is
# immutable and requires an entry to be deleted and recreated.

[ac60034b-3498-11ed-a50d-919b4b1a5ec0]
# my-problematic-entry
exclude = true

# Remap the uuid of this entry to a new uuid on Kanidm
#
# map_uuid = <uuid>

# Remap the name of this entry to a new name on Kanidm
#
# map_name = <name>

# Remap the gidnumber for groups, and uidnumber for users
#
# map_gidnumber = <number>
