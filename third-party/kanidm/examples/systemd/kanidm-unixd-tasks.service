# You should not need to edit this file. Instead, use a drop-in file:
#   systemctl edit kanidm-unixd-tasks.service

[Unit]
Description=Kanidm Local Tasks
After=chronyd.service ntpd.service network-online.target kanidm-unixd.service

[Service]
User=root
Type=notify
ExecStart=/usr/local/sbin/kanidm_unixd_tasks

ReadWritePaths=/home /var/run/kanidm-unixd

CapabilityBoundingSet=CAP_CHOWN CAP_FOWNER CAP_DAC_OVERRIDE CAP_DAC_READ_SEARCH
MemoryDenyWriteExecute=true
NoNewPrivileges=true
PrivateDevices=true
PrivateNetwork=true
PrivateTmp=true
ProtectClock=true
ProtectControlGroups=true
ProtectHostname=true
ProtectKernelLogs=true
ProtectKernelModules=true
ProtectKernelTunables=true
ProtectSystem=strict
RestrictAddressFamilies=AF_UNIX

[Install]
WantedBy=multi-user.target
