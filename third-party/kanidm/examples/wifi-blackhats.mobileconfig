<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ConsentText</key>
	<dict>
		<key>default</key>
		<string>Consent Message</string>
	</dict>
	<key>PayloadContent</key>
	<array>
		<dict>
			<key>AutoJoin</key>
			<true/>
			<key>CaptiveBypass</key>
			<true/>
			<key>EAPClientConfiguration</key>
			<dict>
				<key>AcceptEAPTypes</key>
				<array>
					<integer>25</integer>
				</array>
				<key>OuterIdentity</key>
				<string>outerident</string>
				<key>PayloadCertificateAnchorUUID</key>
				<array>
					<string>3507F18D-291F-4C03-885A-F3A33CD3A811</string>
				</array>
				<key>TLSMaximumVersion</key>
				<string>1.2</string>
				<key>TLSMinimumVersion</key>
				<string>1.0</string>
				<key>TLSTrustedServerNames</key>
				<array/>
				<key>UserName</key>
				<string>username</string>
				<key>UserPassword</key>
				<string>password</string>
			</dict>
			<key>EncryptionType</key>
			<string>WPA2</string>
			<key>HIDDEN_NETWORK</key>
			<false/>
			<key>IsHotspot</key>
			<false/>
			<key>PayloadDescription</key>
			<string>Configures Wi-Fi settings</string>
			<key>PayloadDisplayName</key>
			<string>Wi-Fi</string>
			<key>PayloadIdentifier</key>
			<string>com.apple.wifi.managed.74AC9A39-1A8F-48D8-A731-5C7D0491365A</string>
			<key>PayloadType</key>
			<string>com.apple.wifi.managed</string>
			<key>PayloadUUID</key>
			<string>74AC9A39-1A8F-48D8-A731-5C7D0491365A</string>
			<key>PayloadVersion</key>
			<integer>1</integer>
			<key>ProxyType</key>
			<string>None</string>
			<key>SSID_STR</key>
			<string>Blackhats</string>
		</dict>
		<dict>
			<key>PayloadCertificateFileName</key>
			<string>radius-ca.crt</string>
			<key>PayloadContent</key>
			<data>
			LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZjVENDQTFt
			Z0F3SUJBZ0lGQUs2WXZhTXdEUVlKS29aSWh2Y05BUUVMQlFBd1hq
			RUxNQWtHQTFVRUJoTUMKUVZVeEV6QVJCZ05WQkFnVENsRjFaV1Z1
			YzJ4aGJtUXhFVEFQQmdOVkJBY1RDRUp5YVhOaVlXNWxNUkl3RUFZ
			RApWUVFLRXdsQ2JHRmphMmhoZEhNeEV6QVJCZ05WQkFNVENtSm9J
			R3hrWVhBZ1kyRXdIaGNOTVRnd09UQXhNREl4Ck9ETTRXaGNOTWpB
			d09UQXhNREl4T0RNNFdqQmVNUXN3Q1FZRFZRUUdFd0pCVlRFVE1C
			RUdBMVVFQ0JNS1VYVmwKWlc1emJHRnVaREVSTUE4R0ExVUVCeE1J
			UW5KcGMySmhibVV4RWpBUUJnTlZCQW9UQ1VKc1lXTnJhR0YwY3pF
			VApNQkVHQTFVRUF4TUtZbWdnYkdSaGNDQmpZVENDQWlJd0RRWUpL
			b1pJaHZjTkFRRUJCUUFEZ2dJUEFEQ0NBZ29DCmdnSUJBTzhYdm1S
			YmtkNm5BYlVUZi9KYzd6MWd6ampBbW1pTTJteFdLckl2ZE5DMGY3
			WW94akdWMFhPK3hFRkQKWGMyaHZVY0JTaTd5cEVaaEVhS3NjSU9M
			MGVlTEh6U2Nnanh0ZjBiOUZjaXhUbEhVd2FyM05sS0FIUHdKQnpH
			UApmOXZNTGQ4dkdFaG8xd09JYkRSc0MzYitiOGg4SVJPNWVtSTlB
			WTdqbzJlR3IvT2l3UFhPM2l3R29mcHdtZWFNCnhoa0o0NGNHMm9O
			c2JiUjN0bkFMdzVDWm44RXlxbkNOSytERUQvUmhrWmJNSThXbU9N
			dllxTFJJejJIOEM2bVoKaGdFSjNRWEpyRFJNYUxwVE41Vm1zRnFF
			bnJiWnBadXAyQmFraDAwaXZBblJqNUNRdnBYZXIwQU9weHB5ZXlZ
			UQpYNWJXalhVMjM3czE1SVhkeEs5SEhuMHRsVDFoamQ3cUNqWEN6
			eEtxZy9QR2VwU2hZd1F6dzlwUTNmQ20yMzN2CnFYek0zeGQvVW9V
			aVdrbkRCK3k2VVpBU3hRdHZETkF1T2QyV0lqUDA1SzRaaHFxSmdD
			eWFycDJmbFNMb3IzSGUKVVhVWnByWTh1ckN1L2F4TE5uL1ZEcHFr
			d0Rsek95cno2OEl3TEpIeE5CdzVWakltY1V2dEtEQjNMdDJLN0Ft
			MwpvdHVEbVByMEw1MTlqeFBsZTlqOHUyMCtsakwzUW01d1FnQTBU
			ZWwyZlZDUlF6OVgvNVlOVHBiVE55MXZHK3A3CjZ3Vi9jRUk5b2J2
			cE5vUjZWck4ySjRSZE01dzN2NjlQQ0g5aURPZnRrenYrMWlNVE84
			YWd3OVJrY0F4ZmtzWXYKWHdwUHZhQkR1eEk0NlFNUGpvRk43MXgv
			d3AveHN3bmE2UjVUKyt3NGZIOG9qQVEvQWdNQkFBR2pOakEwTUFz
			RwpBMVVkRHdRRUF3SUNCREFTQmdOVkhSTUJBZjhFQ0RBR0FRSC9B
			Z0VBTUJFR0NXQ0dTQUdHK0VJQkFRUUVBd0lDCkJEQU5CZ2txaGtp
			Rzl3MEJBUXNGQUFPQ0FnRUEwUHBtMldWWkJiOXRXUm8yZk9udk52
			NStBNXN2NUltaUJMOEIKYjgxeUdFN2hXZHBQdnlkSGlFSXNqbjJz
			U0t5L1FXTlBtZStGb2RqaEVsMVNyYlg1ZkwxMjdOQmpKVmMxYzZD
			NgpPVmZOOEpXUVZlOHcvKzFJSTRMRmw2dmlxdEd1K0RBdmJHMWVH
			bmVwVEZ4VjBUb25lazA3T3YreGM0TGJ3T29xCmJvYTMySG05alRM
			OE95REkrUFUvNmZnZFc5b1EyK1ZCSDNJQkhYV1pudHhWV3R6TDJt
			c3dJTzcvcDdDazdzRDQKem9kSnRzMkZOeXdHdjlXT3BDaEIrRGd0
			Zks2dXVySzR2Kzd6UDBpRU9jWSt3V0Q1bEx0Z0I2RGJRVGRHQU1o
			MApVNG5DVVRITS9SN3ZxZE5lT093Zlc2YllBVkhJMzluUnN3RWg0
			S3FqWHBITFdDTWJ0Wmw2NU9idEZacCtKS25GCndGWm1FbWk2NjlX
			bm1PWkNDMmlrUWg2TXNJd3RhSlFzKzZhTFA1UWptYythTmtYcDM2
			OVNRK0V1SFpUQk0vMjkKczl5cWVkaHN2QkhiTWFjSG45WnI2QzEy
			LytDSm5FeWdiSDFZQlFMRGQzL1ZNMlZZYTZyL2VEUkxjZ0pBa2M5
			VApMeHZtYzJjWEhWUVVQMDNyT3N1UFNMUUt6a0hJUlZlQktHNlFZ
			RFBlVmRxL1VsOHVDZXcxbFAwb2tLOGJ1M2VECk5iM0YrMUlRWW1T
			UUhhaVdtVzYzYXZyWENNS01sMkhCOU9NWjNNdnlQemN5dGNGdHNR
			cUVjcGRaQlNRaTZyMWcKVEdLTGVEQ1psRUVYa1NKaktmU2VMN3lz
			Z21CT2xBbXp6VmlhS1VJbTQ3T3pZUUhGWFNxb2ZoVzlHdEtJc3hx
			aQo5VHdVaE9jPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
			</data>
			<key>PayloadDescription</key>
			<string>Adds a CA root certificate</string>
			<key>PayloadDisplayName</key>
			<string>bh ldap ca</string>
			<key>PayloadIdentifier</key>
			<string>com.apple.security.root.3507F18D-291F-4C03-885A-F3A33CD3A811</string>
			<key>PayloadType</key>
			<string>com.apple.security.root</string>
			<key>PayloadUUID</key>
			<string>3507F18D-291F-4C03-885A-F3A33CD3A811</string>
			<key>PayloadVersion</key>
			<integer>1</integer>
		</dict>
	</array>
	<key>PayloadDescription</key>
	<string>Autogenerated Wifi Profile for Radius</string>
	<key>PayloadDisplayName</key>
	<string>wifi-blackhats</string>
	<key>PayloadIdentifier</key>
	<string>kanidm.1CAAD07A-89C7-4A3B-9AAD-C7C1DE7F69AE</string>
	<key>PayloadOrganization</key>
	<string>blackhats.net.au</string>
	<key>PayloadRemovalDisallowed</key>
	<false/>
	<key>PayloadType</key>
	<string>Configuration</string>
	<key>PayloadUUID</key>
	<string>E3A2F3F5-88E9-40B9-985C-1B35BD5314B3</string>
	<key>PayloadVersion</key>
	<integer>1</integer>
</dict>
</plist>
