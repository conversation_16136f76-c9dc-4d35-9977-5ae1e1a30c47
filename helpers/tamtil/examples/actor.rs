//! # TAMTIL Complete Example: Distributed Calculator
//!
//! ## Production-Ready Distributed Actor System
//!
//! This example demonstrates a complete distributed calculator using TAMTIL's
//! production-ready actor system with embedded consensus, zero-copy serialization,
//! and fault tolerance. No mocks, stubs, or fake implementations.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use tokio;

// ============================================================================
// BUSINESS LOGIC: DISTRIBUTED CALCULATOR
// ============================================================================

/// Calculator Action (Production Implementation)
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CalculatorAction {
    pub operation: String,
    pub operand: f64,
}

#[async_trait::async_trait]
impl Action for CalculatorAction {
    type Reaction = CalculatorReaction;
    
    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        // Get current value using rkyv zero-copy deserialization
        let current_bytes = memories.recall("value").await?;
        let current_value = if let Some(bytes) = current_bytes {
            rkyv::from_bytes::<f64, rkyv::rancor::Error>(&bytes)
                .map_err(|e| TamtilError::Deserialization {
                    context: format!("Failed to deserialize current value: {}", e)
                })?
        } else {
            0.0
        };
        
        // Perform calculation with validation
        let new_value = match self.operation.as_str() {
            "add" => current_value + self.operand,
            "subtract" => current_value - self.operand,
            "multiply" => current_value * self.operand,
            "divide" => {
                if self.operand == 0.0 {
                    return Err(TamtilError::Validation {
                        message: "Division by zero is not allowed".to_string()
                    });
                }
                current_value / self.operand
            }
            "sqrt" => {
                if current_value < 0.0 {
                    return Err(TamtilError::Validation {
                        message: "Cannot take square root of negative number".to_string()
                    });
                }
                current_value.sqrt()
            }
            "clear" => 0.0,
            _ => return Err(TamtilError::Validation {
                message: format!("Unknown operation: {}", self.operation)
            }),
        };
        
        Ok(CalculatorReaction {
            old_value: current_value,
            new_value,
            operation: self.operation.clone(),
            operand: self.operand,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        })
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize calculator action: {}", e)
            })
    }
    
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize calculator action: {}", e)
            })
    }
}

/// Calculator Reaction (Production Implementation)
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CalculatorReaction {
    pub old_value: f64,
    pub new_value: f64,
    pub operation: String,
    pub operand: f64,
    pub timestamp: u64,
}

impl Reaction for CalculatorReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            // Update current value
            MemoryOperation::Set {
                key: "value".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.new_value).unwrap().to_vec(),
            },
            // Increment operation counter
            MemoryOperation::Increment {
                key: "operation_count".to_string(),
                amount: 1,
            },
            // Add to operation history
            MemoryOperation::Append {
                key: "history".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&format!(
                    "[{}] {} {} {} = {}",
                    self.timestamp,
                    self.old_value,
                    self.operation,
                    self.operand,
                    self.new_value
                )).unwrap().to_vec(),
            },
        ]
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize calculator reaction: {}", e)
            })
    }
    
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize calculator reaction: {}", e)
            })
    }
}

// ============================================================================
// MAIN EXAMPLE: DISTRIBUTED CALCULATOR
// ============================================================================

#[tokio::main]
async fn main() -> TamtilResult<()> {
    println!("🚀 TAMTIL Unified Calculator Example");
    println!("====================================");

    // Demonstrate unified development experience
    // Same code works for both CLI and web deployment!

    // For CLI applications (single machine, maximum performance)
    let cli_platform = Platform::local("calculator_cli");
    println!("✅ Created CLI platform (local mode)");

    // For web applications (distributed, fault tolerant)
    let web_platform = Platform::distributed("calculator_web", 1, vec![2, 3]);
    println!("✅ Created web platform (distributed mode)");

    // Choose platform based on deployment context
    // In real apps, this would be determined by configuration
    let platform = if std::env::args().any(|arg| arg == "--distributed") {
        web_platform
    } else {
        cli_platform
    };

    println!("🎯 Using {} mode",
        if platform.is_local() { "LOCAL" } else { "DISTRIBUTED" });
    
    // Create calculator context
    // Same API regardless of deployment mode!
    platform.create_context("calculator_context").await?;
    let context_id = ActorId::new("calculator_context");

    println!("✅ Created calculator context (mode: {})",
        if platform.is_local() { "local" } else { "distributed" });

    // Spawn calculator actor
    // Same actor code works in both CLI and web modes!
    let calculator_actor = GenericActor::<CalculatorAction>::new(ActorId::new("calculator"));
    platform.add_actor_to_context(&context_id, calculator_actor).await?;

    println!("✅ Spawned calculator actor (same code, any deployment)");
    
    // Demonstrate calculator operations
    println!("\n📊 Performing Calculator Operations:");
    println!("====================================");
    
    // Operation 1: Add 10
    let add_action = CalculatorAction {
        operation: "add".to_string(),
        operand: 10.0,
    };
    
    let reaction_bytes = platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        add_action.to_bytes()?
    ).await?;
    
    let reaction = CalculatorReaction::from_bytes(&reaction_bytes)?;
    println!("➕ Add 10: {} + {} = {}", reaction.old_value, reaction.operand, reaction.new_value);
    
    // Operation 2: Multiply by 3
    let multiply_action = CalculatorAction {
        operation: "multiply".to_string(),
        operand: 3.0,
    };
    
    let reaction_bytes = platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        multiply_action.to_bytes()?
    ).await?;
    
    let reaction = CalculatorReaction::from_bytes(&reaction_bytes)?;
    println!("✖️  Multiply by 3: {} × {} = {}", reaction.old_value, reaction.operand, reaction.new_value);
    
    // Operation 3: Square root
    let sqrt_action = CalculatorAction {
        operation: "sqrt".to_string(),
        operand: 0.0, // Not used for sqrt
    };
    
    let reaction_bytes = platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        sqrt_action.to_bytes()?
    ).await?;
    
    let reaction = CalculatorReaction::from_bytes(&reaction_bytes)?;
    println!("√  Square root: √{} = {}", reaction.old_value, reaction.new_value);
    
    // Demonstrate error handling
    println!("\n⚠️  Error Handling Demonstration:");
    println!("=================================");
    
    // Try division by zero
    let divide_by_zero = CalculatorAction {
        operation: "divide".to_string(),
        operand: 0.0,
    };
    
    match platform.send_action(
        &context_id,
        &ActorId::new("calculator"),
        divide_by_zero.to_bytes()?
    ).await {
        Ok(_) => println!("❌ Division by zero should have failed!"),
        Err(TamtilError::Validation { message }) => {
            println!("✅ Correctly caught division by zero: {}", message);
        }
        Err(e) => println!("❌ Unexpected error: {:?}", e),
    }
    
    println!("\n🎉 TAMTIL Unified Experience Demonstrated!");
    println!("==========================================");
    println!("✅ Same actor patterns for CLI and web");
    println!("✅ Transparent local vs distributed execution");
    println!("✅ Zero-copy serialization with rkyv");
    println!("✅ Complete event sourcing and audit trails");
    println!("✅ Production-ready error handling");
    println!("✅ Mode: {}", if platform.is_local() { "LOCAL (CLI)" } else { "DISTRIBUTED (Web)" });
    println!("\n💡 Run with --distributed flag to see distributed mode!");
    println!("💡 Same code, different deployment - that's TAMTIL!");
    
    Ok(())
}
