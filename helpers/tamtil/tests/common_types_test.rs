//! # Common Types Tests
//!
//! ## API-Focused Testing for Core Types
//!
//! Tests for ActorId, Ballot, TamtilError, and other common types.
//! These tests focus on public API behavior and serialization contracts.

use tamtil::*;

// ============================================================================
// ACTOR ID TESTS
// ============================================================================

#[test]
fn test_actor_id_new() {
    let actor_id = ActorId::new("test_actor");
    assert_eq!(actor_id.id, "test_actor");
}

#[test]
fn test_actor_id_display() {
    let actor_id = ActorId::new("calculator/instance_1");
    assert_eq!(format!("{}", actor_id), "calculator/instance_1");
}

#[test]
fn test_actor_id_equality() {
    let id1 = ActorId::new("same_id");
    let id2 = ActorId::new("same_id");
    let id3 = ActorId::new("different_id");
    
    assert_eq!(id1, id2);
    assert_ne!(id1, id3);
}

#[test]
fn test_actor_id_serialization() {
    let original = ActorId::new("test_serialization");
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize ActorId");
    
    let deserialized: ActorId = rkyv::from_bytes::<ActorId, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize ActorId");
    
    assert_eq!(original, deserialized);
}

// ============================================================================
// BALLOT TESTS
// ============================================================================

#[test]
fn test_ballot_default() {
    let ballot = Ballot::default();
    assert_eq!(ballot.n, 0);
    assert_eq!(ballot.pid, 0);
}

#[test]
fn test_ballot_ordering() {
    let ballot1 = Ballot { n: 1, pid: 1 };
    let ballot2 = Ballot { n: 2, pid: 1 };
    let ballot3 = Ballot { n: 1, pid: 2 };
    
    // Higher ballot number wins
    assert!(ballot2 > ballot1);
    
    // Same ballot number, higher pid wins
    assert!(ballot3 > ballot1);
    
    // Transitivity
    assert!(ballot2 > ballot3);
}

#[test]
fn test_ballot_equality() {
    let ballot1 = Ballot { n: 5, pid: 3 };
    let ballot2 = Ballot { n: 5, pid: 3 };
    let ballot3 = Ballot { n: 5, pid: 4 };
    
    assert_eq!(ballot1, ballot2);
    assert_ne!(ballot1, ballot3);
}

#[test]
fn test_ballot_serialization() {
    let original = Ballot { n: 42, pid: 7 };
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize Ballot");
    
    let deserialized: Ballot = rkyv::from_bytes::<Ballot, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize Ballot");
    
    assert_eq!(original, deserialized);
}

// ============================================================================
// ERROR TESTS
// ============================================================================

#[test]
fn test_tamtil_error_display() {
    let error = TamtilError::Serialization {
        context: "Test serialization error".to_string()
    };
    
    let error_string = format!("{}", error);
    assert!(error_string.contains("Serialization failed"));
    assert!(error_string.contains("Test serialization error"));
}

#[test]
fn test_tamtil_error_variants() {
    // Test all error variants can be created
    let _serialization = TamtilError::Serialization {
        context: "test".to_string()
    };
    
    let _deserialization = TamtilError::Deserialization {
        context: "test".to_string()
    };
    
    let _consensus_timeout = TamtilError::ConsensusTimeout {
        operation: "test".to_string()
    };
    
    let _platform_init = TamtilError::PlatformInitFailed {
        reason: "test".to_string()
    };
    
    let _actor_not_found = TamtilError::ActorNotFound {
        actor_id: "test".to_string()
    };
    
    let _context_not_found = TamtilError::ContextNotFound {
        context_id: "test".to_string()
    };
    
    let _storage = TamtilError::Storage {
        message: "test".to_string()
    };
    
    let _network = TamtilError::Network {
        message: "test".to_string()
    };
    
    let _validation = TamtilError::Validation {
        message: "test".to_string()
    };
}

#[test]
fn test_tamtil_result_ok() {
    let result: TamtilResult<i32> = Ok(42);
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 42);
}

#[test]
fn test_tamtil_result_err() {
    let result: TamtilResult<i32> = Err(TamtilError::Validation {
        message: "test error".to_string()
    });
    assert!(result.is_err());
}

// ============================================================================
// NODE ID TESTS
// ============================================================================

#[test]
fn test_node_id_type() {
    let node_id: NodeId = 42;
    assert_eq!(node_id, 42u64);
}

#[test]
fn test_node_id_operations() {
    let node1: NodeId = 1;
    let node2: NodeId = 2;
    
    assert!(node2 > node1);
    assert_eq!(node1 + 1, node2);
}
