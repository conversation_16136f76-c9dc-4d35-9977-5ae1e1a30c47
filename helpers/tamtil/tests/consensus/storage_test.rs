//! # Consensus Storage Tests
//!
//! ## API-Focused Testing for TamtilStorage
//!
//! Tests for storage operations, atomicity, and state management.
//! Focus on public API behavior and ACID properties.

use tamtil::*;

// ============================================================================
// STORAGE INITIALIZATION TESTS
// ============================================================================

#[test]
fn test_storage_default() {
    let storage = TamtilStorage::default();
    
    assert_eq!(storage.get_log_len(), 0);
    assert_eq!(storage.get_decided_idx(), 0);
    assert_eq!(storage.get_compacted_idx(), 0);
    assert!(storage.get_promise().is_none());
    assert!(storage.get_accepted_round().is_none());
    assert!(storage.get_snapshot().is_none());
}

// ============================================================================
// BASIC STORAGE OPERATIONS TESTS
// ============================================================================

#[test]
fn test_storage_append_entry() {
    let mut storage = TamtilStorage::default();
    
    let entry = TamtilEntry {
        reaction_bytes: b"test_reaction".to_vec(),
        actor_id: ActorId::new("test_actor"),
        sequence: 1,
        timestamp: 12345,
        proposer: 1,
    };
    
    let ops = vec![StorageOp::AppendEntry(entry.clone())];
    storage.write_atomically(ops).expect("Failed to append entry");
    
    assert_eq!(storage.get_log_len(), 1);
    
    let entries = storage.get_entries(0, 1).expect("Failed to get entries");
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].sequence, 1);
    assert_eq!(entries[0].actor_id, entry.actor_id);
}

#[test]
fn test_storage_multiple_entries() {
    let mut storage = TamtilStorage::default();
    
    let entries = vec![
        TamtilEntry {
            reaction_bytes: b"reaction1".to_vec(),
            actor_id: ActorId::new("actor1"),
            sequence: 1,
            timestamp: 100,
            proposer: 1,
        },
        TamtilEntry {
            reaction_bytes: b"reaction2".to_vec(),
            actor_id: ActorId::new("actor2"),
            sequence: 2,
            timestamp: 200,
            proposer: 2,
        },
    ];
    
    let ops = entries.iter().map(|e| StorageOp::AppendEntry(e.clone())).collect();
    storage.write_atomically(ops).expect("Failed to append entries");
    
    assert_eq!(storage.get_log_len(), 2);
    
    let retrieved = storage.get_entries(0, 2).expect("Failed to get entries");
    assert_eq!(retrieved.len(), 2);
    assert_eq!(retrieved[0].sequence, 1);
    assert_eq!(retrieved[1].sequence, 2);
}

#[test]
fn test_storage_set_decided_index() {
    let mut storage = TamtilStorage::default();
    
    let ops = vec![StorageOp::SetDecidedIndex(5)];
    storage.write_atomically(ops).expect("Failed to set decided index");
    
    assert_eq!(storage.get_decided_idx(), 5);
}

#[test]
fn test_storage_set_promise() {
    let mut storage = TamtilStorage::default();
    
    let ballot = Ballot { n: 42, pid: 7 };
    let ops = vec![StorageOp::SetPromise(ballot)];
    storage.write_atomically(ops).expect("Failed to set promise");
    
    assert_eq!(storage.get_promise(), Some(ballot));
}

#[test]
fn test_storage_set_accepted_round() {
    let mut storage = TamtilStorage::default();
    
    let ballot = Ballot { n: 100, pid: 3 };
    let ops = vec![StorageOp::SetAcceptedRound(ballot)];
    storage.write_atomically(ops).expect("Failed to set accepted round");
    
    assert_eq!(storage.get_accepted_round(), Some(ballot));
}

#[test]
fn test_storage_set_snapshot() {
    let mut storage = TamtilStorage::default();
    
    let snapshot = TamtilSnapshot {
        state_data: b"test_snapshot".to_vec(),
        timestamp: 12345,
        last_sequence: 10,
    };
    
    let ops = vec![StorageOp::SetSnapshot(snapshot.clone())];
    storage.write_atomically(ops).expect("Failed to set snapshot");
    
    let retrieved = storage.get_snapshot().expect("Snapshot should be set");
    assert_eq!(retrieved.state_data, snapshot.state_data);
    assert_eq!(retrieved.timestamp, snapshot.timestamp);
    assert_eq!(retrieved.last_sequence, snapshot.last_sequence);
}

// ============================================================================
// ATOMIC OPERATIONS TESTS
// ============================================================================

#[test]
fn test_storage_atomic_multiple_operations() {
    let mut storage = TamtilStorage::default();
    
    let entry = TamtilEntry {
        reaction_bytes: b"atomic_test".to_vec(),
        actor_id: ActorId::new("atomic_actor"),
        sequence: 1,
        timestamp: 12345,
        proposer: 1,
    };
    
    let ballot = Ballot { n: 5, pid: 2 };
    
    let ops = vec![
        StorageOp::AppendEntry(entry.clone()),
        StorageOp::SetDecidedIndex(1),
        StorageOp::SetPromise(ballot),
        StorageOp::SetAcceptedRound(ballot),
    ];
    
    storage.write_atomically(ops).expect("Failed to execute atomic operations");
    
    // Verify all operations were applied
    assert_eq!(storage.get_log_len(), 1);
    assert_eq!(storage.get_decided_idx(), 1);
    assert_eq!(storage.get_promise(), Some(ballot));
    assert_eq!(storage.get_accepted_round(), Some(ballot));
    
    let entries = storage.get_entries(0, 1).expect("Failed to get entries");
    assert_eq!(entries[0].sequence, 1);
}

#[test]
fn test_storage_trim_operation() {
    let mut storage = TamtilStorage::default();
    
    // Add multiple entries
    let entries = (1..=5).map(|i| TamtilEntry {
        reaction_bytes: format!("reaction{}", i).into_bytes(),
        actor_id: ActorId::new(&format!("actor{}", i)),
        sequence: i,
        timestamp: i * 100,
        proposer: 1,
    }).collect::<Vec<_>>();
    
    let append_ops: Vec<_> = entries.iter().map(|e| StorageOp::AppendEntry(e.clone())).collect();
    storage.write_atomically(append_ops).expect("Failed to append entries");
    
    assert_eq!(storage.get_log_len(), 5);
    
    // Trim first 2 entries
    let trim_ops = vec![StorageOp::Trim(2)];
    storage.write_atomically(trim_ops).expect("Failed to trim entries");
    
    assert_eq!(storage.get_log_len(), 3);
    assert_eq!(storage.get_compacted_idx(), 2);
    
    // Should only be able to get entries from index 2 onwards
    let remaining = storage.get_entries(2, 3).expect("Failed to get remaining entries");
    assert_eq!(remaining.len(), 3);
    assert_eq!(remaining[0].sequence, 3);
}

// ============================================================================
// EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================

#[test]
fn test_storage_get_entries_empty() {
    let storage = TamtilStorage::default();
    
    let entries = storage.get_entries(0, 0).expect("Failed to get empty range");
    assert_eq!(entries.len(), 0);
}

#[test]
fn test_storage_get_entries_out_of_bounds() {
    let mut storage = TamtilStorage::default();
    
    let entry = TamtilEntry {
        reaction_bytes: b"test".to_vec(),
        actor_id: ActorId::new("test"),
        sequence: 1,
        timestamp: 100,
        proposer: 1,
    };
    
    let ops = vec![StorageOp::AppendEntry(entry)];
    storage.write_atomically(ops).expect("Failed to append entry");
    
    // Try to get entries beyond what exists
    let result = storage.get_entries(0, 5);
    
    // Should handle gracefully (implementation-dependent behavior)
    // The exact behavior depends on the storage implementation
    match result {
        Ok(entries) => {
            // If it succeeds, should return available entries
            assert!(entries.len() <= 1);
        }
        Err(_) => {
            // If it fails, that's also acceptable behavior
        }
    }
}

#[test]
fn test_storage_get_entries_valid_range() {
    let mut storage = TamtilStorage::default();
    
    // Add 5 entries
    let entries = (1..=5).map(|i| TamtilEntry {
        reaction_bytes: format!("reaction{}", i).into_bytes(),
        actor_id: ActorId::new(&format!("actor{}", i)),
        sequence: i,
        timestamp: i * 100,
        proposer: 1,
    }).collect::<Vec<_>>();
    
    let ops: Vec<_> = entries.iter().map(|e| StorageOp::AppendEntry(e.clone())).collect();
    storage.write_atomically(ops).expect("Failed to append entries");
    
    // Get middle range
    let middle = storage.get_entries(1, 3).expect("Failed to get middle range");
    assert_eq!(middle.len(), 3);
    assert_eq!(middle[0].sequence, 2); // 0-indexed, so entry at index 1 has sequence 2
    assert_eq!(middle[1].sequence, 3);
    assert_eq!(middle[2].sequence, 4);
}

#[test]
fn test_storage_state_persistence() {
    let mut storage = TamtilStorage::default();
    
    // Set various state
    let ballot = Ballot { n: 10, pid: 5 };
    let entry = TamtilEntry {
        reaction_bytes: b"persistent_test".to_vec(),
        actor_id: ActorId::new("persistent_actor"),
        sequence: 1,
        timestamp: 12345,
        proposer: 1,
    };
    
    let ops = vec![
        StorageOp::AppendEntry(entry),
        StorageOp::SetDecidedIndex(1),
        StorageOp::SetPromise(ballot),
        StorageOp::SetAcceptedRound(ballot),
    ];
    
    storage.write_atomically(ops).expect("Failed to set state");
    
    // Verify state persists across multiple reads
    for _ in 0..3 {
        assert_eq!(storage.get_log_len(), 1);
        assert_eq!(storage.get_decided_idx(), 1);
        assert_eq!(storage.get_promise(), Some(ballot));
        assert_eq!(storage.get_accepted_round(), Some(ballot));
    }
}
