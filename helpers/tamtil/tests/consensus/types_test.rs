//! # Consensus Types Tests
//!
//! ## API-Focused Testing for Consensus Types
//!
//! Tests for TamtilEntry, TamtilSnapshot, ConsensusMessage, and other consensus types.
//! Focus on serialization contracts and public API behavior.

use tamtil::*;

// ============================================================================
// TAMTIL ENTRY TESTS
// ============================================================================

#[test]
fn test_tamtil_entry_creation() {
    let entry = TamtilEntry {
        reaction_bytes: b"test_reaction".to_vec(),
        actor_id: ActorId::new("test_actor"),
        sequence: 1,
        timestamp: 12345,
        proposer: 1,
    };
    
    assert_eq!(entry.reaction_bytes, b"test_reaction");
    assert_eq!(entry.actor_id.id, "test_actor");
    assert_eq!(entry.sequence, 1);
    assert_eq!(entry.timestamp, 12345);
    assert_eq!(entry.proposer, 1);
}

#[test]
fn test_tamtil_entry_serialization() {
    let original = TamtilEntry {
        reaction_bytes: b"serialization_test".to_vec(),
        actor_id: ActorId::new("serialization_actor"),
        sequence: 42,
        timestamp: 67890,
        proposer: 3,
    };
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize TamtilEntry");
    
    let deserialized: TamtilEntry = rkyv::from_bytes::<TamtilEntry, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize TamtilEntry");
    
    assert_eq!(original.reaction_bytes, deserialized.reaction_bytes);
    assert_eq!(original.actor_id, deserialized.actor_id);
    assert_eq!(original.sequence, deserialized.sequence);
    assert_eq!(original.timestamp, deserialized.timestamp);
    assert_eq!(original.proposer, deserialized.proposer);
}

// ============================================================================
// TAMTIL SNAPSHOT TESTS
// ============================================================================

#[test]
fn test_tamtil_snapshot_create_empty() {
    let entries: Vec<TamtilEntry> = vec![];
    let snapshot = TamtilSnapshot::create(&entries);
    
    assert_eq!(snapshot.last_sequence, 0);
    assert!(snapshot.timestamp > 0); // Should have a valid timestamp
    assert!(!snapshot.state_data.is_empty()); // Should contain serialized empty vec
}

#[test]
fn test_tamtil_snapshot_create_with_entries() {
    let entries = vec![
        TamtilEntry {
            reaction_bytes: b"reaction1".to_vec(),
            actor_id: ActorId::new("actor1"),
            sequence: 1,
            timestamp: 100,
            proposer: 1,
        },
        TamtilEntry {
            reaction_bytes: b"reaction2".to_vec(),
            actor_id: ActorId::new("actor2"),
            sequence: 2,
            timestamp: 200,
            proposer: 2,
        },
    ];
    
    let snapshot = TamtilSnapshot::create(&entries);
    
    assert_eq!(snapshot.last_sequence, 2);
    assert!(snapshot.timestamp > 0);
    assert!(!snapshot.state_data.is_empty());
    
    // Verify we can deserialize the state data back to entries
    let deserialized_entries: Vec<TamtilEntry> = rkyv::from_bytes::<Vec<TamtilEntry>, rkyv::rancor::Error>(&snapshot.state_data)
        .expect("Failed to deserialize snapshot state data");
    
    assert_eq!(deserialized_entries.len(), 2);
    assert_eq!(deserialized_entries[0].sequence, 1);
    assert_eq!(deserialized_entries[1].sequence, 2);
}

#[test]
fn test_tamtil_snapshot_merge() {
    let mut older_snapshot = TamtilSnapshot {
        state_data: b"older_data".to_vec(),
        timestamp: 100,
        last_sequence: 1,
    };
    
    let newer_snapshot = TamtilSnapshot {
        state_data: b"newer_data".to_vec(),
        timestamp: 200,
        last_sequence: 2,
    };
    
    older_snapshot.merge(newer_snapshot.clone());
    
    // Should be replaced with newer snapshot
    assert_eq!(older_snapshot.state_data, b"newer_data");
    assert_eq!(older_snapshot.timestamp, 200);
    assert_eq!(older_snapshot.last_sequence, 2);
}

#[test]
fn test_tamtil_snapshot_merge_older() {
    let mut newer_snapshot = TamtilSnapshot {
        state_data: b"newer_data".to_vec(),
        timestamp: 200,
        last_sequence: 2,
    };
    
    let older_snapshot = TamtilSnapshot {
        state_data: b"older_data".to_vec(),
        timestamp: 100,
        last_sequence: 1,
    };
    
    newer_snapshot.merge(older_snapshot);
    
    // Should keep the newer snapshot
    assert_eq!(newer_snapshot.state_data, b"newer_data");
    assert_eq!(newer_snapshot.timestamp, 200);
    assert_eq!(newer_snapshot.last_sequence, 2);
}

#[test]
fn test_tamtil_snapshot_use_snapshots() {
    assert!(TamtilSnapshot::use_snapshots());
}

#[test]
fn test_tamtil_snapshot_serialization() {
    let original = TamtilSnapshot {
        state_data: b"test_state_data".to_vec(),
        timestamp: 12345,
        last_sequence: 42,
    };
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize TamtilSnapshot");
    
    let deserialized: TamtilSnapshot = rkyv::from_bytes::<TamtilSnapshot, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize TamtilSnapshot");
    
    assert_eq!(original.state_data, deserialized.state_data);
    assert_eq!(original.timestamp, deserialized.timestamp);
    assert_eq!(original.last_sequence, deserialized.last_sequence);
}

// ============================================================================
// ROLE AND PHASE TESTS
// ============================================================================

#[test]
fn test_role_variants() {
    let leader = Role::Leader;
    let follower = Role::Follower;
    
    assert_ne!(leader, follower);
    assert_eq!(leader, Role::Leader);
    assert_eq!(follower, Role::Follower);
}

#[test]
fn test_phase_variants() {
    let prepare = Phase::Prepare;
    let accept = Phase::Accept;
    let recover = Phase::Recover;
    let none = Phase::None;
    
    assert_ne!(prepare, accept);
    assert_ne!(accept, recover);
    assert_ne!(recover, none);
    
    assert_eq!(prepare, Phase::Prepare);
    assert_eq!(accept, Phase::Accept);
    assert_eq!(recover, Phase::Recover);
    assert_eq!(none, Phase::None);
}

// ============================================================================
// CONSENSUS MESSAGE TESTS
// ============================================================================

#[test]
fn test_consensus_message_prepare_req() {
    let ballot = Ballot { n: 1, pid: 1 };
    let message = ConsensusMessage::PrepareReq { ballot };
    
    if let ConsensusMessage::PrepareReq { ballot: msg_ballot } = message {
        assert_eq!(msg_ballot, ballot);
    } else {
        panic!("Expected PrepareReq message");
    }
}

#[test]
fn test_consensus_message_serialization() {
    let ballot = Ballot { n: 42, pid: 7 };
    let entry = TamtilEntry {
        reaction_bytes: b"test".to_vec(),
        actor_id: ActorId::new("test"),
        sequence: 1,
        timestamp: 100,
        proposer: 1,
    };
    
    let messages = vec![
        ConsensusMessage::PrepareReq { ballot },
        ConsensusMessage::Promise {
            ballot,
            accepted_entries: vec![entry.clone()],
            last_accepted_ballot: Some(ballot),
        },
        ConsensusMessage::Accept {
            ballot,
            entries: vec![entry],
            first_idx: 0,
        },
        ConsensusMessage::Accepted {
            ballot,
            first_idx: 0,
            length: 1,
        },
        ConsensusMessage::Decide {
            ballot,
            decided_idx: 1,
        },
        ConsensusMessage::Heartbeat { ballot },
    ];
    
    for original in messages {
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
            .expect("Failed to serialize ConsensusMessage");
        
        let _deserialized: ConsensusMessage = rkyv::from_bytes::<ConsensusMessage, rkyv::rancor::Error>(&bytes)
            .expect("Failed to deserialize ConsensusMessage");
        
        // Note: We can't easily compare the full message due to complex structure,
        // but successful serialization/deserialization validates the contract
    }
}
