//! # Consensus Protocol Tests
//!
//! ## API-Focused Testing for TamtilConsensus
//!
//! Tests for consensus protocol behavior, leader election, log replication,
//! and message handling. Focus on public API behavior and consensus properties.

use tamtil::*;

// ============================================================================
// CONSENSUS INITIALIZATION TESTS
// ============================================================================

#[test]
fn test_consensus_new() {
    let consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    let (role, phase) = consensus.get_state();
    assert_eq!(role, Role::Follower);
    assert_eq!(phase, Phase::None);
    
    let ballot = consensus.get_ballot();
    assert_eq!(ballot.n, 0);
    assert_eq!(ballot.pid, 1); // Should use node ID as proposer ID
}

#[test]
fn test_consensus_single_node() {
    let consensus = TamtilConsensus::new(1, vec![]);
    
    let (role, phase) = consensus.get_state();
    assert_eq!(role, Role::Follower);
    assert_eq!(phase, Phase::None);
}

// ============================================================================
// PROPOSAL AND LEADER ELECTION TESTS
// ============================================================================

#[test]
fn test_consensus_propose_entry_as_follower() {
    let mut consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    let reaction_bytes = b"test_reaction".to_vec();
    let actor_id = ActorId::new("test_actor");
    
    let result = consensus.propose_entry(reaction_bytes.clone(), actor_id.clone());
    assert!(result.is_ok());
    
    // Should attempt to become leader
    let messages = consensus.take_outgoing_messages();
    assert_eq!(messages.len(), 2); // Should send PrepareReq to 2 peers
    
    // Verify messages are PrepareReq
    for (to_node, message) in messages {
        assert!(to_node == 2 || to_node == 3);
        if let ConsensusMessage::PrepareReq { ballot } = message {
            assert!(ballot.n > 0);
            assert_eq!(ballot.pid, 1);
        } else {
            panic!("Expected PrepareReq message");
        }
    }
}

#[test]
fn test_consensus_leader_election_success() {
    let mut node1 = TamtilConsensus::new(1, vec![2, 3]);
    let mut node2 = TamtilConsensus::new(2, vec![1, 3]);
    let mut node3 = TamtilConsensus::new(3, vec![1, 2]);
    
    // Node 1 tries to become leader
    let reaction_bytes = b"test_reaction".to_vec();
    let actor_id = ActorId::new("test_actor");
    
    node1.propose_entry(reaction_bytes, actor_id).unwrap();
    
    // Get prepare messages from node 1
    let prepare_messages = node1.take_outgoing_messages();
    assert_eq!(prepare_messages.len(), 2);
    
    // Send prepare messages to peers
    for (to_node, message) in prepare_messages {
        match to_node {
            2 => node2.handle_message(1, message).unwrap(),
            3 => node3.handle_message(1, message).unwrap(),
            _ => panic!("Unexpected target node"),
        }
    }
    
    // Get promise responses
    let node2_messages = node2.take_outgoing_messages();
    let node3_messages = node3.take_outgoing_messages();
    
    // Send promises back to node 1
    for (_, message) in node2_messages {
        node1.handle_message(2, message).unwrap();
    }
    for (_, message) in node3_messages {
        node1.handle_message(3, message).unwrap();
    }
    
    // Node 1 should now be leader
    let (role, phase) = node1.get_state();
    assert_eq!(role, Role::Leader);
    assert_eq!(phase, Phase::Accept);
    
    // Should have accept messages ready
    let accept_messages = node1.take_outgoing_messages();
    assert!(!accept_messages.is_empty());
}

// ============================================================================
// MESSAGE HANDLING TESTS
// ============================================================================

#[test]
fn test_consensus_handle_prepare_request() {
    let mut follower = TamtilConsensus::new(2, vec![1, 3]);
    
    let ballot = Ballot { n: 1, pid: 1 };
    let prepare_msg = ConsensusMessage::PrepareReq { ballot };
    
    let result = follower.handle_message(1, prepare_msg);
    assert!(result.is_ok());
    
    // Should send promise response
    let messages = follower.take_outgoing_messages();
    assert_eq!(messages.len(), 1);
    
    let (to_node, message) = &messages[0];
    assert_eq!(*to_node, 1);
    
    if let ConsensusMessage::Promise { ballot: promise_ballot, .. } = message {
        assert_eq!(*promise_ballot, ballot);
    } else {
        panic!("Expected Promise message");
    }
}

#[test]
fn test_consensus_handle_heartbeat() {
    let mut follower = TamtilConsensus::new(2, vec![1, 3]);
    
    let ballot = Ballot { n: 5, pid: 1 };
    let heartbeat_msg = ConsensusMessage::Heartbeat { ballot };
    
    let result = follower.handle_message(1, heartbeat_msg);
    assert!(result.is_ok());
    
    // Heartbeat handling should not produce outgoing messages
    let messages = follower.take_outgoing_messages();
    assert_eq!(messages.len(), 0);
}

#[test]
fn test_consensus_handle_decide_message() {
    let mut follower = TamtilConsensus::new(2, vec![1, 3]);
    
    let ballot = Ballot { n: 3, pid: 1 };
    let decide_msg = ConsensusMessage::Decide {
        ballot,
        decided_idx: 5,
    };
    
    let result = follower.handle_message(1, decide_msg);
    assert!(result.is_ok());
    
    // Should not produce outgoing messages
    let messages = follower.take_outgoing_messages();
    assert_eq!(messages.len(), 0);
}

// ============================================================================
// LOG REPLICATION TESTS
// ============================================================================

#[test]
fn test_consensus_get_decided_entries_empty() {
    let consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    let decided_entries = consensus.get_decided_entries();
    assert_eq!(decided_entries.len(), 0);
}

#[test]
fn test_consensus_message_queue() {
    let mut consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    // Initially no messages
    let messages = consensus.take_outgoing_messages();
    assert_eq!(messages.len(), 0);
    
    // Propose entry to generate messages
    let reaction_bytes = b"queue_test".to_vec();
    let actor_id = ActorId::new("queue_actor");
    
    consensus.propose_entry(reaction_bytes, actor_id).unwrap();
    
    // Should have messages now
    let messages = consensus.take_outgoing_messages();
    assert!(!messages.is_empty());
    
    // Taking again should be empty
    let messages = consensus.take_outgoing_messages();
    assert_eq!(messages.len(), 0);
}

// ============================================================================
// BALLOT MANAGEMENT TESTS
// ============================================================================

#[test]
fn test_consensus_ballot_progression() {
    let mut consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    let initial_ballot = consensus.get_ballot();
    assert_eq!(initial_ballot.n, 0);
    
    // Propose entry should increment ballot
    let reaction_bytes = b"ballot_test".to_vec();
    let actor_id = ActorId::new("ballot_actor");
    
    consensus.propose_entry(reaction_bytes, actor_id).unwrap();
    
    let new_ballot = consensus.get_ballot();
    assert!(new_ballot.n > initial_ballot.n);
    assert_eq!(new_ballot.pid, 1);
}

// ============================================================================
// EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================

#[test]
fn test_consensus_handle_invalid_message_source() {
    let mut consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    let ballot = Ballot { n: 1, pid: 4 }; // From unknown node 4
    let prepare_msg = ConsensusMessage::PrepareReq { ballot };
    
    // Should handle gracefully (implementation-dependent)
    let result = consensus.handle_message(4, prepare_msg);
    
    // Either succeeds (accepts unknown nodes) or fails gracefully
    match result {
        Ok(_) => {
            // If it accepts, that's fine
        }
        Err(_) => {
            // If it rejects, that's also fine
        }
    }
}

#[test]
fn test_consensus_multiple_proposals() {
    let mut consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    // Propose multiple entries
    for i in 1..=3 {
        let reaction_bytes = format!("reaction_{}", i).into_bytes();
        let actor_id = ActorId::new(&format!("actor_{}", i));
        
        let result = consensus.propose_entry(reaction_bytes, actor_id);
        assert!(result.is_ok());
    }
    
    // Should have generated messages
    let messages = consensus.take_outgoing_messages();
    assert!(!messages.is_empty());
}

#[test]
fn test_consensus_state_consistency() {
    let consensus = TamtilConsensus::new(1, vec![2, 3]);
    
    // State should be consistent across multiple reads
    for _ in 0..5 {
        let (role, phase) = consensus.get_state();
        assert_eq!(role, Role::Follower);
        assert_eq!(phase, Phase::None);
        
        let ballot = consensus.get_ballot();
        assert_eq!(ballot.pid, 1);
    }
}

// ============================================================================
// INTEGRATION SCENARIO TESTS
// ============================================================================

#[test]
fn test_consensus_three_node_scenario() {
    let mut node1 = TamtilConsensus::new(1, vec![2, 3]);
    let mut node2 = TamtilConsensus::new(2, vec![1, 3]);
    let mut node3 = TamtilConsensus::new(3, vec![1, 2]);
    
    // All nodes start as followers
    assert_eq!(node1.get_state().0, Role::Follower);
    assert_eq!(node2.get_state().0, Role::Follower);
    assert_eq!(node3.get_state().0, Role::Follower);
    
    // Node 1 proposes
    node1.propose_entry(b"test".to_vec(), ActorId::new("test")).unwrap();
    
    // Simulate message exchange for leader election
    let messages1 = node1.take_outgoing_messages();
    for (to, msg) in messages1 {
        match to {
            2 => node2.handle_message(1, msg).unwrap(),
            3 => node3.handle_message(1, msg).unwrap(),
            _ => {}
        }
    }
    
    // Send responses back
    let messages2 = node2.take_outgoing_messages();
    let messages3 = node3.take_outgoing_messages();
    
    for (_, msg) in messages2 {
        node1.handle_message(2, msg).unwrap();
    }
    for (_, msg) in messages3 {
        node1.handle_message(3, msg).unwrap();
    }
    
    // Node 1 should become leader
    assert_eq!(node1.get_state().0, Role::Leader);
}
