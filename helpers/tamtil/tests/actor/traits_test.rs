//! # Actor Traits Tests
//!
//! ## API-Focused Testing for Actor Traits
//!
//! Tests for Action, Reaction, and Actor traits using concrete implementations.
//! Focus on trait contracts and public API behavior.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST IMPLEMENTATIONS
// ============================================================================

/// Test Action Implementation
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct TestAction {
    pub value: i32,
    pub operation: String,
}

#[async_trait::async_trait]
impl Action for TestAction {
    type Reaction = TestReaction;

    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        // Simple test logic: get current counter and apply operation
        let current = memories.get_counter("test_counter").await.unwrap_or(0);
        
        let new_value = match self.operation.as_str() {
            "add" => current + self.value,
            "subtract" => current - self.value,
            "multiply" => current * self.value,
            _ => return Err(TamtilError::Validation {
                message: format!("Unknown operation: {}", self.operation)
            }),
        };
        
        Ok(TestReaction {
            old_value: current,
            new_value,
            operation: self.operation.clone(),
        })
    }

    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        if self.operation.is_empty() {
            return Err(TamtilError::Validation {
                message: "Operation cannot be empty".to_string()
            });
        }
        Ok(())
    }

    fn get_subscriptions(&self) -> Vec<Subscription> {
        if self.operation == "subscribe_test" {
            vec![Subscription {
                publisher_id: ActorId::new("test_publisher"),
                reaction_type: "TestReaction".to_string(),
            }]
        } else {
            Vec::new()
        }
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize TestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize TestAction: {}", e)
            })
    }
}

/// Test Reaction Implementation
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct TestReaction {
    pub old_value: i32,
    pub new_value: i32,
    pub operation: String,
}

impl Reaction for TestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "test_counter".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.new_value).unwrap().to_vec(),
            },
            MemoryOperation::Increment {
                key: "operation_count".to_string(),
                amount: 1,
            },
            MemoryOperation::Append {
                key: "history".to_string(),
                value: format!("{} -> {}", self.old_value, self.new_value).into_bytes(),
            },
        ]
    }

    async fn react(&self, subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        // Test reaction handling
        println!("Reacting to subscribed reaction from: {}", subscribed_reaction.publisher_id);
        Ok(())
    }

    fn reaction_type(&self) -> String {
        "TestReaction".to_string()
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize TestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize TestReaction: {}", e)
            })
    }
}

/// Test Actor Implementation
pub struct TestActor {
    id: ActorId,
}

impl TestActor {
    pub fn new(id: ActorId) -> Self {
        Self { id }
    }
}

#[async_trait::async_trait]
impl Actor for TestActor {
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Vec<u8>> {
        // Deserialize action
        let action = TestAction::from_bytes(&action_bytes)?;
        
        // Validate action
        action.validate(&self.id)?;
        
        // Handle subscriptions
        for subscription in action.get_subscriptions() {
            // In a real implementation, this would be handled by the system
            println!("Would subscribe to: {:?}", subscription);
        }
        
        // Execute action
        let reaction = action.act(memories, subscriptions).await?;
        
        // Apply memory operations
        memories.remember(reaction.remember()).await?;
        
        // Return serialized reaction
        reaction.to_bytes()
    }

    async fn handle_subscribed_reaction(&self, subscribed_reaction: &SubscribedReaction, memories: &ActorMemories) -> TamtilResult<()> {
        // Deserialize the reaction and handle it
        if let Ok(reaction) = TestReaction::from_bytes(&subscribed_reaction.reaction_bytes) {
            reaction.react(subscribed_reaction, memories).await?;
        }
        Ok(())
    }

    fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// ACTION TRAIT TESTS
// ============================================================================

#[tokio::test]
async fn test_action_act() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    let action = TestAction {
        value: 5,
        operation: "add".to_string(),
    };
    
    let reaction = action.act(&memories, &subscriptions).await.unwrap();
    
    assert_eq!(reaction.old_value, 0);
    assert_eq!(reaction.new_value, 5);
    assert_eq!(reaction.operation, "add");
}

#[tokio::test]
async fn test_action_validate_success() {
    let action = TestAction {
        value: 10,
        operation: "add".to_string(),
    };
    
    let actor_id = ActorId::new("test_actor");
    let result = action.validate(&actor_id);
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_action_validate_failure() {
    let action = TestAction {
        value: 10,
        operation: "".to_string(), // Empty operation should fail
    };
    
    let actor_id = ActorId::new("test_actor");
    let result = action.validate(&actor_id);
    
    assert!(result.is_err());
    if let Err(TamtilError::Validation { message }) = result {
        assert_eq!(message, "Operation cannot be empty");
    }
}

#[test]
fn test_action_get_subscriptions() {
    let action_with_subscription = TestAction {
        value: 1,
        operation: "subscribe_test".to_string(),
    };
    
    let subscriptions = action_with_subscription.get_subscriptions();
    assert_eq!(subscriptions.len(), 1);
    assert_eq!(subscriptions[0].publisher_id.id, "test_publisher");
    assert_eq!(subscriptions[0].reaction_type, "TestReaction");
    
    let action_without_subscription = TestAction {
        value: 1,
        operation: "add".to_string(),
    };
    
    let subscriptions = action_without_subscription.get_subscriptions();
    assert_eq!(subscriptions.len(), 0);
}

#[test]
fn test_action_serialization() {
    let original = TestAction {
        value: 42,
        operation: "multiply".to_string(),
    };
    
    let bytes = original.to_bytes().unwrap();
    let deserialized = TestAction::from_bytes(&bytes).unwrap();
    
    assert_eq!(original.value, deserialized.value);
    assert_eq!(original.operation, deserialized.operation);
}

// ============================================================================
// REACTION TRAIT TESTS
// ============================================================================

#[test]
fn test_reaction_remember() {
    let reaction = TestReaction {
        old_value: 5,
        new_value: 10,
        operation: "add".to_string(),
    };
    
    let operations = reaction.remember();
    assert_eq!(operations.len(), 3);
    
    // Check Set operation
    if let MemoryOperation::Set { key, .. } = &operations[0] {
        assert_eq!(key, "test_counter");
    } else {
        panic!("Expected Set operation");
    }
    
    // Check Increment operation
    if let MemoryOperation::Increment { key, amount } = &operations[1] {
        assert_eq!(key, "operation_count");
        assert_eq!(*amount, 1);
    } else {
        panic!("Expected Increment operation");
    }
    
    // Check Append operation
    if let MemoryOperation::Append { key, .. } = &operations[2] {
        assert_eq!(key, "history");
    } else {
        panic!("Expected Append operation");
    }
}

#[tokio::test]
async fn test_reaction_react() {
    let reaction = TestReaction {
        old_value: 1,
        new_value: 2,
        operation: "test".to_string(),
    };
    
    let subscribed_reaction = SubscribedReaction {
        publisher_id: ActorId::new("publisher"),
        reaction_bytes: b"test".to_vec(),
        reaction_type: "TestReaction".to_string(),
    };
    
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let result = reaction.react(&subscribed_reaction, &memories).await;
    assert!(result.is_ok());
}

#[test]
fn test_reaction_type() {
    let reaction = TestReaction {
        old_value: 0,
        new_value: 1,
        operation: "test".to_string(),
    };
    
    assert_eq!(reaction.reaction_type(), "TestReaction");
}

#[test]
fn test_reaction_serialization() {
    let original = TestReaction {
        old_value: 100,
        new_value: 200,
        operation: "double".to_string(),
    };
    
    let bytes = original.to_bytes().unwrap();
    let deserialized = TestReaction::from_bytes(&bytes).unwrap();
    
    assert_eq!(original.old_value, deserialized.old_value);
    assert_eq!(original.new_value, deserialized.new_value);
    assert_eq!(original.operation, deserialized.operation);
}

// ============================================================================
// ACTOR TRAIT TESTS
// ============================================================================

#[tokio::test]
async fn test_actor_process() {
    let actor = TestActor::new(ActorId::new("test_actor"));
    let memories = ActorMemories::new(ActorId::new("test_actor"));
    let subscriptions = SubscriptionManager::new();
    
    let action = TestAction {
        value: 15,
        operation: "add".to_string(),
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let reaction_bytes = actor.process(action_bytes, &memories, &subscriptions).await.unwrap();
    
    let reaction = TestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.old_value, 0);
    assert_eq!(reaction.new_value, 15);
    assert_eq!(reaction.operation, "add");
    
    // Verify memory was updated
    let counter_value = memories.get_counter("test_counter").await.unwrap();
    assert_eq!(counter_value, 15);
}

#[tokio::test]
async fn test_actor_handle_subscribed_reaction() {
    let actor = TestActor::new(ActorId::new("test_actor"));
    let memories = ActorMemories::new(ActorId::new("test_actor"));
    
    let test_reaction = TestReaction {
        old_value: 1,
        new_value: 2,
        operation: "test".to_string(),
    };
    
    let subscribed_reaction = SubscribedReaction {
        publisher_id: ActorId::new("publisher"),
        reaction_bytes: test_reaction.to_bytes().unwrap(),
        reaction_type: "TestReaction".to_string(),
    };
    
    let result = actor.handle_subscribed_reaction(&subscribed_reaction, &memories).await;
    assert!(result.is_ok());
}

#[test]
fn test_actor_id() {
    let actor_id = ActorId::new("my_test_actor");
    let actor = TestActor::new(actor_id.clone());
    
    assert_eq!(actor.id(), &actor_id);
}
