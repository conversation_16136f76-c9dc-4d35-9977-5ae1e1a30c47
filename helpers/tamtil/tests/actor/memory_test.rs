//! # Actor Memory Tests
//!
//! ## API-Focused Testing for ActorMemories
//!
//! Tests for memory operations, event sourcing, and state management.
//! Focus on public API behavior and ACID properties.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST REACTION FOR MEMORY TESTS
// ============================================================================

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct MemoryTestReaction {
    pub test_data: String,
}

impl Reaction for MemoryTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "test_data".to_string(),
                value: self.test_data.as_bytes().to_vec(),
            },
            MemoryOperation::Increment {
                key: "reaction_count".to_string(),
                amount: 1,
            },
        ]
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize MemoryTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize MemoryTestReaction: {}", e)
            })
    }
}

// ============================================================================
// MEMORY INITIALIZATION TESTS
// ============================================================================

#[tokio::test]
async fn test_actor_memories_new() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id.clone());
    
    // Should start with empty state
    let value = memories.recall("nonexistent_key").await.unwrap();
    assert!(value.is_none());
    
    let counter = memories.get_counter("nonexistent_counter").await.unwrap();
    assert_eq!(counter, 0);
    
    let list = memories.get_list("nonexistent_list").await.unwrap();
    assert_eq!(list.len(), 0);
}

// ============================================================================
// BASIC MEMORY OPERATIONS TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_set_and_recall() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations = vec![
        MemoryOperation::Set {
            key: "test_key".to_string(),
            value: b"test_value".to_vec(),
        }
    ];
    
    memories.remember(operations).await.unwrap();
    
    let value = memories.recall("test_key").await.unwrap();
    assert_eq!(value, Some(b"test_value".to_vec()));
}

#[tokio::test]
async fn test_memory_set_overwrite() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Set initial value
    let operations = vec![
        MemoryOperation::Set {
            key: "overwrite_key".to_string(),
            value: b"initial_value".to_vec(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    // Overwrite with new value
    let operations = vec![
        MemoryOperation::Set {
            key: "overwrite_key".to_string(),
            value: b"new_value".to_vec(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    let value = memories.recall("overwrite_key").await.unwrap();
    assert_eq!(value, Some(b"new_value".to_vec()));
}

#[tokio::test]
async fn test_memory_delete() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Set a value
    let operations = vec![
        MemoryOperation::Set {
            key: "delete_key".to_string(),
            value: b"to_be_deleted".to_vec(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    // Verify it exists
    let value = memories.recall("delete_key").await.unwrap();
    assert!(value.is_some());
    
    // Delete it
    let operations = vec![
        MemoryOperation::Delete {
            key: "delete_key".to_string(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    // Verify it's gone
    let value = memories.recall("delete_key").await.unwrap();
    assert!(value.is_none());
}

// ============================================================================
// COUNTER OPERATIONS TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_increment() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations = vec![
        MemoryOperation::Increment {
            key: "test_counter".to_string(),
            amount: 5,
        }
    ];
    
    memories.remember(operations).await.unwrap();
    
    let counter_value = memories.get_counter("test_counter").await.unwrap();
    assert_eq!(counter_value, 5);
}

#[tokio::test]
async fn test_memory_increment_multiple() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations = vec![
        MemoryOperation::Increment {
            key: "multi_counter".to_string(),
            amount: 3,
        },
        MemoryOperation::Increment {
            key: "multi_counter".to_string(),
            amount: 7,
        },
        MemoryOperation::Increment {
            key: "multi_counter".to_string(),
            amount: 2,
        },
    ];
    
    memories.remember(operations).await.unwrap();
    
    let counter_value = memories.get_counter("multi_counter").await.unwrap();
    assert_eq!(counter_value, 12);
}

#[tokio::test]
async fn test_memory_increment_negative() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Start with positive value
    let operations = vec![
        MemoryOperation::Increment {
            key: "negative_counter".to_string(),
            amount: 10,
        }
    ];
    memories.remember(operations).await.unwrap();
    
    // Decrement
    let operations = vec![
        MemoryOperation::Increment {
            key: "negative_counter".to_string(),
            amount: -3,
        }
    ];
    memories.remember(operations).await.unwrap();
    
    let counter_value = memories.get_counter("negative_counter").await.unwrap();
    assert_eq!(counter_value, 7);
}

// ============================================================================
// LIST OPERATIONS TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_append() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations = vec![
        MemoryOperation::Append {
            key: "test_list".to_string(),
            value: b"item1".to_vec(),
        },
        MemoryOperation::Append {
            key: "test_list".to_string(),
            value: b"item2".to_vec(),
        },
    ];
    
    memories.remember(operations).await.unwrap();
    
    let list = memories.get_list("test_list").await.unwrap();
    assert_eq!(list.len(), 2);
    assert_eq!(list[0], b"item1");
    assert_eq!(list[1], b"item2");
}

#[tokio::test]
async fn test_memory_remove_from_list() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Add items to list
    let operations = vec![
        MemoryOperation::Append {
            key: "remove_list".to_string(),
            value: b"item0".to_vec(),
        },
        MemoryOperation::Append {
            key: "remove_list".to_string(),
            value: b"item1".to_vec(),
        },
        MemoryOperation::Append {
            key: "remove_list".to_string(),
            value: b"item2".to_vec(),
        },
    ];
    memories.remember(operations).await.unwrap();
    
    // Remove middle item
    let operations = vec![
        MemoryOperation::Remove {
            key: "remove_list".to_string(),
            index: 1,
        }
    ];
    memories.remember(operations).await.unwrap();
    
    let list = memories.get_list("remove_list").await.unwrap();
    assert_eq!(list.len(), 2);
    assert_eq!(list[0], b"item0");
    assert_eq!(list[1], b"item2");
}

#[tokio::test]
async fn test_memory_remove_from_empty_list() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Try to remove from empty list
    let operations = vec![
        MemoryOperation::Remove {
            key: "empty_list".to_string(),
            index: 0,
        }
    ];
    
    // Should handle gracefully (implementation-dependent)
    let result = memories.remember(operations).await;
    
    // Either succeeds (ignores invalid remove) or fails gracefully
    match result {
        Ok(_) => {
            let list = memories.get_list("empty_list").await.unwrap();
            assert_eq!(list.len(), 0);
        }
        Err(_) => {
            // Failing is also acceptable behavior
        }
    }
}

#[tokio::test]
async fn test_memory_remove_out_of_bounds() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    // Add one item
    let operations = vec![
        MemoryOperation::Append {
            key: "bounds_list".to_string(),
            value: b"single_item".to_vec(),
        }
    ];
    memories.remember(operations).await.unwrap();
    
    // Try to remove out of bounds
    let operations = vec![
        MemoryOperation::Remove {
            key: "bounds_list".to_string(),
            index: 5,
        }
    ];
    
    // Should handle gracefully
    let result = memories.remember(operations).await;
    
    match result {
        Ok(_) => {
            // If it succeeds, list should be unchanged
            let list = memories.get_list("bounds_list").await.unwrap();
            assert_eq!(list.len(), 1);
        }
        Err(_) => {
            // Failing is also acceptable
        }
    }
}

// ============================================================================
// ATOMIC OPERATIONS TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_atomic_operations() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations = vec![
        MemoryOperation::Set {
            key: "atomic_key".to_string(),
            value: b"atomic_value".to_vec(),
        },
        MemoryOperation::Increment {
            key: "atomic_counter".to_string(),
            amount: 10,
        },
        MemoryOperation::Append {
            key: "atomic_list".to_string(),
            value: b"atomic_item".to_vec(),
        },
    ];
    
    memories.remember(operations).await.unwrap();
    
    // All operations should have been applied atomically
    let value = memories.recall("atomic_key").await.unwrap();
    assert_eq!(value, Some(b"atomic_value".to_vec()));
    
    let counter = memories.get_counter("atomic_counter").await.unwrap();
    assert_eq!(counter, 10);
    
    let list = memories.get_list("atomic_list").await.unwrap();
    assert_eq!(list.len(), 1);
    assert_eq!(list[0], b"atomic_item");
}

// ============================================================================
// REACTION MEMORY TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_remember_reaction() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let reaction = MemoryTestReaction {
        test_data: "reaction_test_data".to_string(),
    };
    
    memories.remember_reaction(&reaction).await.unwrap();
    
    // Check that reaction's memory operations were applied
    let value = memories.recall("test_data").await.unwrap();
    assert_eq!(value, Some(b"reaction_test_data".to_vec()));
    
    let counter = memories.get_counter("reaction_count").await.unwrap();
    assert_eq!(counter, 1);
    
    // Check that reaction bytes were stored
    let reactions = memories.get_reactions().await.unwrap();
    assert_eq!(reactions.len(), 1);
    
    let stored_reaction = MemoryTestReaction::from_bytes(&reactions[0]).unwrap();
    assert_eq!(stored_reaction.test_data, "reaction_test_data");
}

#[tokio::test]
async fn test_memory_multiple_reactions() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let reaction1 = MemoryTestReaction {
        test_data: "first_reaction".to_string(),
    };
    
    let reaction2 = MemoryTestReaction {
        test_data: "second_reaction".to_string(),
    };
    
    memories.remember_reaction(&reaction1).await.unwrap();
    memories.remember_reaction(&reaction2).await.unwrap();
    
    // Counter should be incremented twice
    let counter = memories.get_counter("reaction_count").await.unwrap();
    assert_eq!(counter, 2);
    
    // Should have both reactions stored
    let reactions = memories.get_reactions().await.unwrap();
    assert_eq!(reactions.len(), 2);
    
    // Last reaction's data should be in memory
    let value = memories.recall("test_data").await.unwrap();
    assert_eq!(value, Some(b"second_reaction".to_vec()));
}

// ============================================================================
// EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================

#[tokio::test]
async fn test_memory_empty_operations() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let operations: Vec<MemoryOperation> = vec![];
    let result = memories.remember(operations).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_memory_recall_nonexistent() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let value = memories.recall("nonexistent").await.unwrap();
    assert!(value.is_none());
}

#[tokio::test]
async fn test_memory_counter_nonexistent() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let counter = memories.get_counter("nonexistent").await.unwrap();
    assert_eq!(counter, 0);
}

#[tokio::test]
async fn test_memory_list_nonexistent() {
    let actor_id = ActorId::new("test_actor");
    let memories = ActorMemories::new(actor_id);
    
    let list = memories.get_list("nonexistent").await.unwrap();
    assert_eq!(list.len(), 0);
}
