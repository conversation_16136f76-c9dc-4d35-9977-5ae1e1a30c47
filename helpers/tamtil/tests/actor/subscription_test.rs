//! # Actor Subscription Tests
//!
//! ## API-Focused Testing for SubscriptionManager
//!
//! Tests for subscription management, publisher-subscriber relationships,
//! and subscription lifecycle. Focus on public API behavior.

use tamtil::*;

// ============================================================================
// SUBSCRIPTION MANAGER INITIALIZATION TESTS
// ============================================================================

#[test]
fn test_subscription_manager_new() {
    let manager = SubscriptionManager::new();
    
    let actor_id = ActorId::new("test_actor");
    let subscribers = manager.get_subscribers(&actor_id);
    assert_eq!(subscribers.len(), 0);
    
    let publishers = manager.get_publishers(&actor_id);
    assert_eq!(publishers.len(), 0);
}

#[test]
fn test_subscription_manager_default() {
    let manager = SubscriptionManager::default();
    
    let actor_id = ActorId::new("test_actor");
    let subscribers = manager.get_subscribers(&actor_id);
    assert_eq!(subscribers.len(), 0);
    
    let publishers = manager.get_publishers(&actor_id);
    assert_eq!(publishers.len(), 0);
}

// ============================================================================
// BASIC SUBSCRIPTION TESTS
// ============================================================================

#[test]
fn test_subscription_subscribe() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    let reaction_type = "TestReaction".to_string();
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: reaction_type.clone(),
    };
    
    manager.subscribe(&subscriber, subscription);
    
    // Check that subscriber is listed for publisher
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1);
    assert!(subscribers.contains(&subscriber));
    
    // Check that publisher is listed for subscriber
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 1);
    assert!(publishers.contains(&publisher));
}

#[test]
fn test_subscription_multiple_subscribers() {
    let manager = SubscriptionManager::new();
    
    let subscriber1 = ActorId::new("subscriber1");
    let subscriber2 = ActorId::new("subscriber2");
    let subscriber3 = ActorId::new("subscriber3");
    let publisher = ActorId::new("publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "TestReaction".to_string(),
    };
    
    manager.subscribe(&subscriber1, subscription.clone());
    manager.subscribe(&subscriber2, subscription.clone());
    manager.subscribe(&subscriber3, subscription);
    
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 3);
    assert!(subscribers.contains(&subscriber1));
    assert!(subscribers.contains(&subscriber2));
    assert!(subscribers.contains(&subscriber3));
}

#[test]
fn test_subscription_multiple_publishers() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher1 = ActorId::new("publisher1");
    let publisher2 = ActorId::new("publisher2");
    let publisher3 = ActorId::new("publisher3");
    
    let subscription1 = Subscription {
        publisher_id: publisher1.clone(),
        reaction_type: "TestReaction1".to_string(),
    };
    
    let subscription2 = Subscription {
        publisher_id: publisher2.clone(),
        reaction_type: "TestReaction2".to_string(),
    };
    
    let subscription3 = Subscription {
        publisher_id: publisher3.clone(),
        reaction_type: "TestReaction3".to_string(),
    };
    
    manager.subscribe(&subscriber, subscription1);
    manager.subscribe(&subscriber, subscription2);
    manager.subscribe(&subscriber, subscription3);
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 3);
    assert!(publishers.contains(&publisher1));
    assert!(publishers.contains(&publisher2));
    assert!(publishers.contains(&publisher3));
}

// ============================================================================
// UNSUBSCRIPTION TESTS
// ============================================================================

#[test]
fn test_subscription_unsubscribe() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "TestReaction".to_string(),
    };
    
    // Subscribe first
    manager.subscribe(&subscriber, subscription.clone());
    
    // Verify subscription exists
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1);
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 1);
    
    // Unsubscribe
    manager.unsubscribe(&subscriber, &subscription);
    
    // Verify subscription is removed
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 0);
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 0);
}

#[test]
fn test_subscription_unsubscribe_partial() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher1 = ActorId::new("publisher1");
    let publisher2 = ActorId::new("publisher2");
    
    let subscription1 = Subscription {
        publisher_id: publisher1.clone(),
        reaction_type: "TestReaction1".to_string(),
    };
    
    let subscription2 = Subscription {
        publisher_id: publisher2.clone(),
        reaction_type: "TestReaction2".to_string(),
    };
    
    // Subscribe to both
    manager.subscribe(&subscriber, subscription1.clone());
    manager.subscribe(&subscriber, subscription2.clone());
    
    // Verify both subscriptions
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 2);
    
    // Unsubscribe from one
    manager.unsubscribe(&subscriber, &subscription1);
    
    // Verify only one subscription remains
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 1);
    assert!(publishers.contains(&publisher2));
    assert!(!publishers.contains(&publisher1));
    
    let subscribers1 = manager.get_subscribers(&publisher1);
    assert_eq!(subscribers1.len(), 0);
    
    let subscribers2 = manager.get_subscribers(&publisher2);
    assert_eq!(subscribers2.len(), 1);
}

#[test]
fn test_subscription_unsubscribe_nonexistent() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "TestReaction".to_string(),
    };
    
    // Try to unsubscribe without subscribing first
    manager.unsubscribe(&subscriber, &subscription);
    
    // Should handle gracefully
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 0);
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 0);
}

// ============================================================================
// COMPLEX SUBSCRIPTION SCENARIOS TESTS
// ============================================================================

#[test]
fn test_subscription_many_to_many() {
    let manager = SubscriptionManager::new();
    
    let subscribers = vec![
        ActorId::new("sub1"),
        ActorId::new("sub2"),
        ActorId::new("sub3"),
    ];
    
    let publishers = vec![
        ActorId::new("pub1"),
        ActorId::new("pub2"),
        ActorId::new("pub3"),
    ];
    
    // Each subscriber subscribes to each publisher
    for (i, subscriber) in subscribers.iter().enumerate() {
        for (j, publisher) in publishers.iter().enumerate() {
            let subscription = Subscription {
                publisher_id: publisher.clone(),
                reaction_type: format!("Reaction{}_{}", i, j),
            };
            manager.subscribe(subscriber, subscription);
        }
    }
    
    // Verify each publisher has all subscribers
    for publisher in &publishers {
        let subs = manager.get_subscribers(publisher);
        assert_eq!(subs.len(), 3);
        for subscriber in &subscribers {
            assert!(subs.contains(subscriber));
        }
    }
    
    // Verify each subscriber has all publishers
    for subscriber in &subscribers {
        let pubs = manager.get_publishers(subscriber);
        assert_eq!(pubs.len(), 3);
        for publisher in &publishers {
            assert!(pubs.contains(publisher));
        }
    }
}

#[test]
fn test_subscription_duplicate_subscription() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "TestReaction".to_string(),
    };
    
    // Subscribe twice with same subscription
    manager.subscribe(&subscriber, subscription.clone());
    manager.subscribe(&subscriber, subscription);
    
    // Should only have one subscription (no duplicates)
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1);
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 1);
}

#[test]
fn test_subscription_different_reaction_types() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    
    let subscription1 = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "ReactionType1".to_string(),
    };
    
    let subscription2 = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "ReactionType2".to_string(),
    };
    
    manager.subscribe(&subscriber, subscription1);
    manager.subscribe(&subscriber, subscription2);
    
    // Should be treated as separate subscriptions
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1); // Same subscriber, but potentially multiple reaction types
    
    let publishers = manager.get_publishers(&subscriber);
    assert_eq!(publishers.len(), 1); // Same publisher, but multiple reaction types
}

// ============================================================================
// SUBSCRIPTION DATA STRUCTURE TESTS
// ============================================================================

#[test]
fn test_subscription_creation() {
    let publisher_id = ActorId::new("test_publisher");
    let reaction_type = "TestReactionType".to_string();
    
    let subscription = Subscription {
        publisher_id: publisher_id.clone(),
        reaction_type: reaction_type.clone(),
    };
    
    assert_eq!(subscription.publisher_id, publisher_id);
    assert_eq!(subscription.reaction_type, reaction_type);
}

#[test]
fn test_subscribed_reaction_creation() {
    let publisher_id = ActorId::new("test_publisher");
    let reaction_bytes = b"test_reaction_data".to_vec();
    let reaction_type = "TestReactionType".to_string();
    
    let subscribed_reaction = SubscribedReaction {
        publisher_id: publisher_id.clone(),
        reaction_bytes: reaction_bytes.clone(),
        reaction_type: reaction_type.clone(),
    };
    
    assert_eq!(subscribed_reaction.publisher_id, publisher_id);
    assert_eq!(subscribed_reaction.reaction_bytes, reaction_bytes);
    assert_eq!(subscribed_reaction.reaction_type, reaction_type);
}

// ============================================================================
// EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================

#[test]
fn test_subscription_empty_actor_ids() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("");
    let publisher = ActorId::new("");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "TestReaction".to_string(),
    };
    
    manager.subscribe(&subscriber, subscription);
    
    // Should handle empty IDs gracefully
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1);
    assert!(subscribers.contains(&subscriber));
}

#[test]
fn test_subscription_empty_reaction_type() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("subscriber");
    let publisher = ActorId::new("publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "".to_string(),
    };
    
    manager.subscribe(&subscriber, subscription);
    
    // Should handle empty reaction type gracefully
    let subscribers = manager.get_subscribers(&publisher);
    assert_eq!(subscribers.len(), 1);
}

#[test]
fn test_subscription_get_nonexistent_actor() {
    let manager = SubscriptionManager::new();
    
    let nonexistent_actor = ActorId::new("nonexistent");
    
    let subscribers = manager.get_subscribers(&nonexistent_actor);
    assert_eq!(subscribers.len(), 0);
    
    let publishers = manager.get_publishers(&nonexistent_actor);
    assert_eq!(publishers.len(), 0);
}

#[test]
fn test_subscription_lifecycle() {
    let manager = SubscriptionManager::new();
    
    let subscriber = ActorId::new("lifecycle_subscriber");
    let publisher = ActorId::new("lifecycle_publisher");
    
    let subscription = Subscription {
        publisher_id: publisher.clone(),
        reaction_type: "LifecycleReaction".to_string(),
    };
    
    // Start with no subscriptions
    assert_eq!(manager.get_subscribers(&publisher).len(), 0);
    assert_eq!(manager.get_publishers(&subscriber).len(), 0);
    
    // Subscribe
    manager.subscribe(&subscriber, subscription.clone());
    assert_eq!(manager.get_subscribers(&publisher).len(), 1);
    assert_eq!(manager.get_publishers(&subscriber).len(), 1);
    
    // Unsubscribe
    manager.unsubscribe(&subscriber, &subscription);
    assert_eq!(manager.get_subscribers(&publisher).len(), 0);
    assert_eq!(manager.get_publishers(&subscriber).len(), 0);
    
    // Subscribe again
    manager.subscribe(&subscriber, subscription);
    assert_eq!(manager.get_subscribers(&publisher).len(), 1);
    assert_eq!(manager.get_publishers(&subscriber).len(), 1);
}
