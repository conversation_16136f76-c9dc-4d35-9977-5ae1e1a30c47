//! # Actor Core Tests
//!
//! ## API-Focused Testing for GenericActor and ActorHandle
//!
//! Tests for actor core functionality, message processing, and handle management.
//! Focus on public API behavior and actor lifecycle.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST ACTION AND REACTION FOR CORE TESTS
// ============================================================================

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CoreTestAction {
    pub command: String,
    pub value: i32,
}

#[async_trait::async_trait]
impl Action for CoreTestAction {
    type Reaction = CoreTestReaction;

    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        match self.command.as_str() {
            "set" => {
                Ok(CoreTestReaction {
                    result: format!("Set value to {}", self.value),
                    new_value: self.value,
                })
            }
            "get" => {
                let current = memories.get_counter("test_value").await.unwrap_or(0);
                Ok(CoreTestReaction {
                    result: format!("Current value is {}", current),
                    new_value: current,
                })
            }
            "add" => {
                let current = memories.get_counter("test_value").await.unwrap_or(0);
                let new_value = current + self.value;
                Ok(CoreTestReaction {
                    result: format!("Added {} to {}, result: {}", self.value, current, new_value),
                    new_value,
                })
            }
            "error" => {
                Err(TamtilError::Validation {
                    message: "Intentional test error".to_string()
                })
            }
            _ => {
                Err(TamtilError::Validation {
                    message: format!("Unknown command: {}", self.command)
                })
            }
        }
    }

    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        if self.command.is_empty() {
            return Err(TamtilError::Validation {
                message: "Command cannot be empty".to_string()
            });
        }
        Ok(())
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize CoreTestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize CoreTestAction: {}", e)
            })
    }
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CoreTestReaction {
    pub result: String,
    pub new_value: i32,
}

impl Reaction for CoreTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "test_value".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.new_value).unwrap().to_vec(),
            },
            MemoryOperation::Increment {
                key: "operation_count".to_string(),
                amount: 1,
            },
            MemoryOperation::Append {
                key: "operation_log".to_string(),
                value: self.result.as_bytes().to_vec(),
            },
        ]
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize CoreTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize CoreTestReaction: {}", e)
            })
    }
}

// ============================================================================
// GENERIC ACTOR TESTS
// ============================================================================

#[tokio::test]
async fn test_generic_actor_creation() {
    let actor_id = ActorId::new("test_generic_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    
    assert_eq!(actor.id(), &actor_id);
}

#[tokio::test]
async fn test_generic_actor_process_success() {
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    let action = CoreTestAction {
        command: "set".to_string(),
        value: 42,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let reaction_bytes = actor.process(action_bytes, &memories, &subscriptions).await.unwrap();
    
    let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.result, "Set value to 42");
    assert_eq!(reaction.new_value, 42);
    
    // Verify memory was updated
    let stored_value = memories.get_counter("test_value").await.unwrap();
    assert_eq!(stored_value, 42);
    
    let operation_count = memories.get_counter("operation_count").await.unwrap();
    assert_eq!(operation_count, 1);
}

#[tokio::test]
async fn test_generic_actor_process_validation_error() {
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    let action = CoreTestAction {
        command: "".to_string(), // Empty command should fail validation
        value: 10,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let result = actor.process(action_bytes, &memories, &subscriptions).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::Validation { message }) = result {
        assert_eq!(message, "Command cannot be empty");
    }
}

#[tokio::test]
async fn test_generic_actor_process_action_error() {
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    let action = CoreTestAction {
        command: "error".to_string(),
        value: 0,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let result = actor.process(action_bytes, &memories, &subscriptions).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::Validation { message }) = result {
        assert_eq!(message, "Intentional test error");
    }
}

#[tokio::test]
async fn test_generic_actor_multiple_operations() {
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    // Set initial value
    let set_action = CoreTestAction {
        command: "set".to_string(),
        value: 10,
    };
    
    let action_bytes = set_action.to_bytes().unwrap();
    actor.process(action_bytes, &memories, &subscriptions).await.unwrap();
    
    // Add to the value
    let add_action = CoreTestAction {
        command: "add".to_string(),
        value: 5,
    };
    
    let action_bytes = add_action.to_bytes().unwrap();
    let reaction_bytes = actor.process(action_bytes, &memories, &subscriptions).await.unwrap();
    
    let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.new_value, 15);
    assert!(reaction.result.contains("Added 5 to 10, result: 15"));
    
    // Verify final state
    let final_value = memories.get_counter("test_value").await.unwrap();
    assert_eq!(final_value, 15);
    
    let operation_count = memories.get_counter("operation_count").await.unwrap();
    assert_eq!(operation_count, 2);
}

// ============================================================================
// ACTOR HANDLE TESTS
// ============================================================================

#[tokio::test]
async fn test_actor_handle_creation() {
    let actor_id = ActorId::new("handle_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    assert_eq!(handle.id(), &actor_id);
}

#[tokio::test]
async fn test_actor_handle_send() {
    let actor_id = ActorId::new("handle_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    let action = CoreTestAction {
        command: "set".to_string(),
        value: 100,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let reaction_bytes = handle.send(action_bytes).await.unwrap();
    
    let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.result, "Set value to 100");
    assert_eq!(reaction.new_value, 100);
}

#[tokio::test]
async fn test_actor_handle_send_subscribed_reaction() {
    let actor_id = ActorId::new("handle_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    let test_reaction = CoreTestReaction {
        result: "Subscribed reaction test".to_string(),
        new_value: 999,
    };
    
    let subscribed_reaction = SubscribedReaction {
        publisher_id: ActorId::new("publisher"),
        reaction_bytes: test_reaction.to_bytes().unwrap(),
        reaction_type: "CoreTestReaction".to_string(),
    };
    
    let result = handle.send_subscribed_reaction(subscribed_reaction).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_actor_handle_multiple_sends() {
    let actor_id = ActorId::new("handle_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    // Send multiple actions
    for i in 1..=5 {
        let action = CoreTestAction {
            command: "add".to_string(),
            value: i,
        };
        
        let action_bytes = action.to_bytes().unwrap();
        let reaction_bytes = handle.send(action_bytes).await.unwrap();
        
        let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
        
        // Each add should accumulate
        let expected_total = (1..=i).sum();
        assert_eq!(reaction.new_value, expected_total);
    }
}

// ============================================================================
// ERROR HANDLING TESTS
// ============================================================================

#[tokio::test]
async fn test_generic_actor_invalid_action_bytes() {
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id);
    let subscriptions = SubscriptionManager::new();
    
    let invalid_bytes = b"invalid_action_data".to_vec();
    let result = actor.process(invalid_bytes, &memories, &subscriptions).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::Deserialization { .. }) = result {
        // Expected deserialization error
    } else {
        panic!("Expected deserialization error");
    }
}

#[tokio::test]
async fn test_actor_handle_send_error() {
    let actor_id = ActorId::new("handle_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    let action = CoreTestAction {
        command: "error".to_string(),
        value: 0,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let result = handle.send(action_bytes).await;
    
    assert!(result.is_err());
}

// ============================================================================
// INTEGRATION TESTS
// ============================================================================

#[tokio::test]
async fn test_actor_state_persistence() {
    let actor_id = ActorId::new("persistence_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    // Set initial value
    let set_action = CoreTestAction {
        command: "set".to_string(),
        value: 50,
    };
    handle.send(set_action.to_bytes().unwrap()).await.unwrap();
    
    // Get value to verify persistence
    let get_action = CoreTestAction {
        command: "get".to_string(),
        value: 0,
    };
    
    let reaction_bytes = handle.send(get_action.to_bytes().unwrap()).await.unwrap();
    let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
    
    assert_eq!(reaction.new_value, 50);
    assert!(reaction.result.contains("Current value is 50"));
}

#[tokio::test]
async fn test_actor_concurrent_operations() {
    let actor_id = ActorId::new("concurrent_test_actor");
    let actor = GenericActor::<CoreTestAction>::new(actor_id.clone());
    let memories = ActorMemories::new(actor_id.clone());
    let subscriptions = SubscriptionManager::new();
    
    let handle = ActorHandle::new(actor, memories, subscriptions).await;
    
    // Send multiple concurrent operations
    let mut tasks = vec![];
    
    for i in 1..=10 {
        let handle_clone = handle.clone();
        let task = tokio::spawn(async move {
            let action = CoreTestAction {
                command: "add".to_string(),
                value: 1,
            };
            
            handle_clone.send(action.to_bytes().unwrap()).await
        });
        tasks.push(task);
    }
    
    // Wait for all tasks to complete
    for task in tasks {
        let result = task.await.unwrap();
        assert!(result.is_ok());
    }
    
    // Final value should be 10 (10 additions of 1)
    let get_action = CoreTestAction {
        command: "get".to_string(),
        value: 0,
    };
    
    let reaction_bytes = handle.send(get_action.to_bytes().unwrap()).await.unwrap();
    let reaction = CoreTestReaction::from_bytes(&reaction_bytes).unwrap();
    
    assert_eq!(reaction.new_value, 10);
}
