//! # HTTP Server Tests
//!
//! ## API-Focused Testing for HttpServer
//!
//! Tests for HTTP server functionality, routing, and integration with actors.
//! Focus on public API behavior and HTTP protocol compliance.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use std::sync::Arc;

// ============================================================================
// TEST ACTION AND REACTION FOR HTTP TESTS
// ============================================================================

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct HttpTestAction {
    pub message: String,
    pub echo_data: Vec<u8>,
}

#[async_trait::async_trait]
impl Action for HttpTestAction {
    type Reaction = HttpTestReaction;

    async fn act(&self, _memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        Ok(HttpTestReaction {
            response: format!("Processed: {}", self.message),
            echoed_data: self.echo_data.clone(),
        })
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize HttpTestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize HttpTestAction: {}", e)
            })
    }
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct HttpTestReaction {
    pub response: String,
    pub echoed_data: Vec<u8>,
}

impl Reaction for HttpTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Increment {
                key: "http_requests".to_string(),
                amount: 1,
            },
            MemoryOperation::Append {
                key: "request_log".to_string(),
                value: self.response.as_bytes().to_vec(),
            },
        ]
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize HttpTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize HttpTestReaction: {}", e)
            })
    }
}

// ============================================================================
// HTTP SERVER CREATION TESTS
// ============================================================================

#[tokio::test]
async fn test_http_server_creation() {
    // Create a local platform and context for testing
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    // Get context reference
    let context = platform.get_context(&context_id).await.unwrap();
    
    // Create HTTP server
    let server = HttpServer::new("127.0.0.1:0".to_string(), context);
    
    // Server should be created successfully
    assert!(server.is_ok());
}

#[tokio::test]
async fn test_http_server_add_route() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    // Add test actor to context
    let test_actor = GenericActor::<HttpTestAction>::new(ActorId::new("http_test_actor"));
    platform.add_actor_to_context(&context_id, test_actor).await.unwrap();
    
    let context = platform.get_context(&context_id).await.unwrap();
    let mut server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Add route
    let actor_id = ActorId::new("http_test_actor");
    server.add_route("/test", actor_id.clone());
    
    // Route should be added successfully (no panic or error)
    // We can't easily verify the internal state without accessing private fields
}

// ============================================================================
// HTTP REQUEST HANDLING TESTS (INTEGRATION STYLE)
// ============================================================================

#[tokio::test]
async fn test_http_server_integration_get_request() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    // Add test actor
    let test_actor = GenericActor::<HttpTestAction>::new(ActorId::new("echo_actor"));
    platform.add_actor_to_context(&context_id, test_actor).await.unwrap();
    
    let context = platform.get_context(&context_id).await.unwrap();
    let mut server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Add route
    server.add_route("/echo", ActorId::new("echo_actor"));
    
    // Start server in background
    let server_handle = tokio::spawn(async move {
        // In a real test, we would start the server and get its actual port
        // For now, we'll test the server creation and route addition
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    });
    
    // Wait a bit for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    // Clean up
    server_handle.abort();
}

// ============================================================================
// HTTP ACTION CONVERSION TESTS
// ============================================================================

#[tokio::test]
async fn test_http_action_from_get_request() {
    // Test conversion of GET request to HttpAction
    // This would typically be done inside the server's request handler
    
    let query_params = vec![
        ("param1".to_string(), "value1".to_string()),
        ("param2".to_string(), "value2".to_string()),
    ];
    
    let http_action = HttpAction::Get { query_params: query_params.clone() };
    
    // Verify the action was created correctly
    if let HttpAction::Get { query_params: action_params } = http_action {
        assert_eq!(action_params, query_params);
    } else {
        panic!("Expected Get action");
    }
}

#[tokio::test]
async fn test_http_action_from_post_request() {
    // Test conversion of POST request to HttpAction
    let body_data = b"test post body".to_vec();
    let content_type = "application/json".to_string();
    
    let http_action = HttpAction::Post {
        body: body_data.clone(),
        content_type: content_type.clone(),
    };
    
    // Verify the action was created correctly
    if let HttpAction::Post { body, content_type: action_content_type } = http_action {
        assert_eq!(body, body_data);
        assert_eq!(action_content_type, content_type);
    } else {
        panic!("Expected Post action");
    }
}

// ============================================================================
// HTTP RESPONSE TESTS
// ============================================================================

#[tokio::test]
async fn test_http_response_from_reaction() {
    // Test that reactions can be converted to HTTP responses
    let reaction = HttpTestReaction {
        response: "Test response".to_string(),
        echoed_data: b"echoed".to_vec(),
    };
    
    let reaction_bytes = reaction.to_bytes().unwrap();
    
    // In a real HTTP server, these bytes would be used as the response body
    assert!(!reaction_bytes.is_empty());
    
    // Verify we can deserialize the response
    let deserialized = HttpTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(deserialized.response, "Test response");
    assert_eq!(deserialized.echoed_data, b"echoed");
}

// ============================================================================
// HTTP SERVER ERROR HANDLING TESTS
// ============================================================================

#[tokio::test]
async fn test_http_server_invalid_address() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    let context = platform.get_context(&context_id).await.unwrap();
    
    // Try to create server with invalid address
    let result = HttpServer::new("invalid_address".to_string(), context);
    
    // Should handle invalid address gracefully
    // The exact behavior depends on the implementation
    match result {
        Ok(_) => {
            // If it succeeds, that's fine (might parse as hostname)
        }
        Err(_) => {
            // If it fails, that's also expected behavior
        }
    }
}

// ============================================================================
// HTTP SERVER ROUTING TESTS
// ============================================================================

#[tokio::test]
async fn test_http_server_multiple_routes() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    // Add multiple test actors
    let actor1 = GenericActor::<HttpTestAction>::new(ActorId::new("actor1"));
    let actor2 = GenericActor::<HttpTestAction>::new(ActorId::new("actor2"));
    let actor3 = GenericActor::<HttpTestAction>::new(ActorId::new("actor3"));
    
    platform.add_actor_to_context(&context_id, actor1).await.unwrap();
    platform.add_actor_to_context(&context_id, actor2).await.unwrap();
    platform.add_actor_to_context(&context_id, actor3).await.unwrap();
    
    let context = platform.get_context(&context_id).await.unwrap();
    let mut server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Add multiple routes
    server.add_route("/api/actor1", ActorId::new("actor1"));
    server.add_route("/api/actor2", ActorId::new("actor2"));
    server.add_route("/api/actor3", ActorId::new("actor3"));
    
    // All routes should be added successfully
}

#[tokio::test]
async fn test_http_server_route_patterns() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    let test_actor = GenericActor::<HttpTestAction>::new(ActorId::new("pattern_actor"));
    platform.add_actor_to_context(&context_id, test_actor).await.unwrap();
    
    let context = platform.get_context(&context_id).await.unwrap();
    let mut server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Add routes with different patterns
    let actor_id = ActorId::new("pattern_actor");
    server.add_route("/", actor_id.clone());
    server.add_route("/api", actor_id.clone());
    server.add_route("/api/v1", actor_id.clone());
    server.add_route("/api/v1/users", actor_id.clone());
    server.add_route("/api/v1/users/:id", actor_id.clone());
    
    // All route patterns should be added successfully
}

// ============================================================================
// HTTP SERVER LIFECYCLE TESTS
// ============================================================================

#[tokio::test]
async fn test_http_server_start_stop() {
    let platform = Platform::local("http_test_platform");
    platform.create_context("http_test_context").await.unwrap();
    let context_id = ActorId::new("http_test_context");
    
    let context = platform.get_context(&context_id).await.unwrap();
    let server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Start server in background task
    let server_task = tokio::spawn(async move {
        // In a real implementation, this would start the HTTP server
        // For testing, we'll simulate server lifecycle
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        Ok::<(), TamtilError>(())
    });
    
    // Let server run briefly
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    // Stop server
    server_task.abort();
    
    // Server should stop gracefully
}

// ============================================================================
// HTTP INTEGRATION WITH ACTOR SYSTEM TESTS
// ============================================================================

#[tokio::test]
async fn test_http_actor_integration() {
    let platform = Platform::local("http_integration_platform");
    platform.create_context("http_integration_context").await.unwrap();
    let context_id = ActorId::new("http_integration_context");
    
    // Create an actor that handles HTTP actions
    let http_actor = GenericActor::<HttpTestAction>::new(ActorId::new("http_integration_actor"));
    platform.add_actor_to_context(&context_id, http_actor).await.unwrap();
    
    // Test direct actor interaction (simulating what HTTP server would do)
    let test_action = HttpTestAction {
        message: "HTTP integration test".to_string(),
        echo_data: b"integration_data".to_vec(),
    };
    
    let action_bytes = test_action.to_bytes().unwrap();
    let reaction_bytes = platform.send_action(
        &context_id,
        &ActorId::new("http_integration_actor"),
        action_bytes
    ).await.unwrap();
    
    let reaction = HttpTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.response, "Processed: HTTP integration test");
    assert_eq!(reaction.echoed_data, b"integration_data");
}

#[tokio::test]
async fn test_http_server_context_integration() {
    let platform = Platform::local("context_integration_platform");
    platform.create_context("context_integration_context").await.unwrap();
    let context_id = ActorId::new("context_integration_context");
    
    // Add actor to context
    let integration_actor = GenericActor::<HttpTestAction>::new(ActorId::new("integration_actor"));
    platform.add_actor_to_context(&context_id, integration_actor).await.unwrap();
    
    // Get context and create server
    let context = platform.get_context(&context_id).await.unwrap();
    let mut server = HttpServer::new("127.0.0.1:0".to_string(), context).unwrap();
    
    // Add route to the actor
    server.add_route("/integration", ActorId::new("integration_actor"));
    
    // Server should be properly integrated with the context and actor
}
