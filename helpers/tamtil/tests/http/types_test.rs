//! # HTTP Types Tests
//!
//! ## API-Focused Testing for HTTP Types
//!
//! Tests for HttpAction and other HTTP-related types.
//! Focus on serialization contracts and public API behavior.

use tamtil::*;

// ============================================================================
// HTTP ACTION TESTS
// ============================================================================

#[test]
fn test_http_action_get_creation() {
    let get_action = HttpAction::Get {
        query_params: vec![
            ("param1".to_string(), "value1".to_string()),
            ("param2".to_string(), "value2".to_string()),
        ],
    };
    
    if let HttpAction::Get { query_params } = get_action {
        assert_eq!(query_params.len(), 2);
        assert_eq!(query_params[0], ("param1".to_string(), "value1".to_string()));
        assert_eq!(query_params[1], ("param2".to_string(), "value2".to_string()));
    } else {
        panic!("Expected Get variant");
    }
}

#[test]
fn test_http_action_post_creation() {
    let body_data = b"test_post_body".to_vec();
    let post_action = HttpAction::Post {
        body: body_data.clone(),
        content_type: "application/json".to_string(),
    };
    
    if let HttpAction::Post { body, content_type } = post_action {
        assert_eq!(body, body_data);
        assert_eq!(content_type, "application/json");
    } else {
        panic!("Expected Post variant");
    }
}

#[test]
fn test_http_action_get_empty_params() {
    let get_action = HttpAction::Get {
        query_params: vec![],
    };
    
    if let HttpAction::Get { query_params } = get_action {
        assert_eq!(query_params.len(), 0);
    } else {
        panic!("Expected Get variant");
    }
}

#[test]
fn test_http_action_post_empty_body() {
    let post_action = HttpAction::Post {
        body: vec![],
        content_type: "text/plain".to_string(),
    };
    
    if let HttpAction::Post { body, content_type } = post_action {
        assert_eq!(body.len(), 0);
        assert_eq!(content_type, "text/plain");
    } else {
        panic!("Expected Post variant");
    }
}

// ============================================================================
// HTTP ACTION SERIALIZATION TESTS
// ============================================================================

#[test]
fn test_http_action_get_serialization() {
    let original = HttpAction::Get {
        query_params: vec![
            ("key1".to_string(), "value1".to_string()),
            ("key2".to_string(), "value2".to_string()),
        ],
    };
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize HttpAction::Get");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction::Get");
    
    if let HttpAction::Get { query_params } = deserialized {
        assert_eq!(query_params.len(), 2);
        assert_eq!(query_params[0], ("key1".to_string(), "value1".to_string()));
        assert_eq!(query_params[1], ("key2".to_string(), "value2".to_string()));
    } else {
        panic!("Expected Get variant after deserialization");
    }
}

#[test]
fn test_http_action_post_serialization() {
    let body_data = b"serialization_test_body".to_vec();
    let original = HttpAction::Post {
        body: body_data.clone(),
        content_type: "application/octet-stream".to_string(),
    };
    
    // Test rkyv serialization
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize HttpAction::Post");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction::Post");
    
    if let HttpAction::Post { body, content_type } = deserialized {
        assert_eq!(body, body_data);
        assert_eq!(content_type, "application/octet-stream");
    } else {
        panic!("Expected Post variant after deserialization");
    }
}

#[test]
fn test_http_action_get_with_special_characters() {
    let original = HttpAction::Get {
        query_params: vec![
            ("special".to_string(), "value with spaces".to_string()),
            ("unicode".to_string(), "测试".to_string()),
            ("symbols".to_string(), "!@#$%^&*()".to_string()),
        ],
    };
    
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize HttpAction with special characters");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction with special characters");
    
    if let HttpAction::Get { query_params } = deserialized {
        assert_eq!(query_params.len(), 3);
        assert_eq!(query_params[0].1, "value with spaces");
        assert_eq!(query_params[1].1, "测试");
        assert_eq!(query_params[2].1, "!@#$%^&*()");
    } else {
        panic!("Expected Get variant");
    }
}

#[test]
fn test_http_action_post_with_binary_data() {
    let binary_data = vec![0u8, 1, 2, 3, 255, 254, 253];
    let original = HttpAction::Post {
        body: binary_data.clone(),
        content_type: "application/octet-stream".to_string(),
    };
    
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&original)
        .expect("Failed to serialize HttpAction with binary data");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction with binary data");
    
    if let HttpAction::Post { body, content_type } = deserialized {
        assert_eq!(body, binary_data);
        assert_eq!(content_type, "application/octet-stream");
    } else {
        panic!("Expected Post variant");
    }
}

// ============================================================================
// HTTP ACTION EDGE CASES TESTS
// ============================================================================

#[test]
fn test_http_action_get_duplicate_param_keys() {
    let get_action = HttpAction::Get {
        query_params: vec![
            ("duplicate".to_string(), "value1".to_string()),
            ("duplicate".to_string(), "value2".to_string()),
            ("duplicate".to_string(), "value3".to_string()),
        ],
    };
    
    if let HttpAction::Get { query_params } = get_action {
        assert_eq!(query_params.len(), 3);
        // All entries should be preserved, even with duplicate keys
        assert_eq!(query_params[0].0, "duplicate");
        assert_eq!(query_params[1].0, "duplicate");
        assert_eq!(query_params[2].0, "duplicate");
        assert_eq!(query_params[0].1, "value1");
        assert_eq!(query_params[1].1, "value2");
        assert_eq!(query_params[2].1, "value3");
    } else {
        panic!("Expected Get variant");
    }
}

#[test]
fn test_http_action_post_empty_content_type() {
    let post_action = HttpAction::Post {
        body: b"test_body".to_vec(),
        content_type: "".to_string(),
    };
    
    if let HttpAction::Post { body, content_type } = post_action {
        assert_eq!(body, b"test_body");
        assert_eq!(content_type, "");
    } else {
        panic!("Expected Post variant");
    }
}

#[test]
fn test_http_action_get_empty_param_values() {
    let get_action = HttpAction::Get {
        query_params: vec![
            ("empty_value".to_string(), "".to_string()),
            ("".to_string(), "empty_key".to_string()),
            ("".to_string(), "".to_string()),
        ],
    };
    
    if let HttpAction::Get { query_params } = get_action {
        assert_eq!(query_params.len(), 3);
        assert_eq!(query_params[0], ("empty_value".to_string(), "".to_string()));
        assert_eq!(query_params[1], ("".to_string(), "empty_key".to_string()));
        assert_eq!(query_params[2], ("".to_string(), "".to_string()));
    } else {
        panic!("Expected Get variant");
    }
}

// ============================================================================
// HTTP ACTION LARGE DATA TESTS
// ============================================================================

#[test]
fn test_http_action_get_many_params() {
    let mut query_params = Vec::new();
    for i in 0..1000 {
        query_params.push((format!("param{}", i), format!("value{}", i)));
    }
    
    let get_action = HttpAction::Get { query_params: query_params.clone() };
    
    // Test serialization with many parameters
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&get_action)
        .expect("Failed to serialize HttpAction with many params");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction with many params");
    
    if let HttpAction::Get { query_params: deserialized_params } = deserialized {
        assert_eq!(deserialized_params.len(), 1000);
        assert_eq!(deserialized_params[0], ("param0".to_string(), "value0".to_string()));
        assert_eq!(deserialized_params[999], ("param999".to_string(), "value999".to_string()));
    } else {
        panic!("Expected Get variant");
    }
}

#[test]
fn test_http_action_post_large_body() {
    // Create a large body (1MB)
    let large_body = vec![42u8; 1024 * 1024];
    let post_action = HttpAction::Post {
        body: large_body.clone(),
        content_type: "application/octet-stream".to_string(),
    };
    
    // Test serialization with large body
    let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&post_action)
        .expect("Failed to serialize HttpAction with large body");
    
    let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
        .expect("Failed to deserialize HttpAction with large body");
    
    if let HttpAction::Post { body, content_type } = deserialized {
        assert_eq!(body.len(), 1024 * 1024);
        assert_eq!(body, large_body);
        assert_eq!(content_type, "application/octet-stream");
    } else {
        panic!("Expected Post variant");
    }
}

// ============================================================================
// HTTP ACTION CONTENT TYPE TESTS
// ============================================================================

#[test]
fn test_http_action_post_common_content_types() {
    let common_content_types = vec![
        "application/json",
        "application/xml",
        "text/html",
        "text/plain",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "application/octet-stream",
        "image/jpeg",
        "image/png",
        "video/mp4",
    ];
    
    for content_type in common_content_types {
        let post_action = HttpAction::Post {
            body: format!("test body for {}", content_type).into_bytes(),
            content_type: content_type.to_string(),
        };
        
        let bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&post_action)
            .expect(&format!("Failed to serialize HttpAction with content type: {}", content_type));
        
        let deserialized: HttpAction = rkyv::from_bytes::<HttpAction, rkyv::rancor::Error>(&bytes)
            .expect(&format!("Failed to deserialize HttpAction with content type: {}", content_type));
        
        if let HttpAction::Post { body: _, content_type: deserialized_type } = deserialized {
            assert_eq!(deserialized_type, content_type);
        } else {
            panic!("Expected Post variant for content type: {}", content_type);
        }
    }
}
