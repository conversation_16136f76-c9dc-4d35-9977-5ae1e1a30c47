//! # Platform Definition Tests
//!
//! ## API-Focused Testing for Platform
//!
//! Tests for platform functionality, context management, and deployment modes.
//! Focus on public API behavior and platform lifecycle.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST ACTION FOR PLATFORM TESTS
// ============================================================================

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct PlatformTestAction {
    pub command: String,
    pub value: i32,
}

#[async_trait::async_trait]
impl Action for PlatformTestAction {
    type Reaction = PlatformTestReaction;

    async fn act(&self, _memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        Ok(PlatformTestReaction {
            response: format!("Executed command: {} with value: {}", self.command, self.value),
            processed_value: self.value * 2,
        })
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize PlatformTestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize PlatformTestAction: {}", e)
            })
    }
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct PlatformTestReaction {
    pub response: String,
    pub processed_value: i32,
}

impl Reaction for PlatformTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "last_response".to_string(),
                value: self.response.as_bytes().to_vec(),
            },
            MemoryOperation::Set {
                key: "last_value".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.processed_value).unwrap().to_vec(),
            },
        ]
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize PlatformTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize PlatformTestReaction: {}", e)
            })
    }
}

// ============================================================================
// PLATFORM CREATION TESTS
// ============================================================================

#[test]
fn test_platform_local_creation() {
    let platform = Platform::local("test_local_platform");
    
    assert!(platform.is_local());
    assert!(!platform.is_distributed());
    assert_eq!(platform.mode(), DeploymentMode::Local);
    assert_eq!(platform.id(), "test_local_platform");
}

#[test]
fn test_platform_distributed_creation() {
    let platform = Platform::distributed("test_distributed_platform", 1, vec![2, 3]);
    
    assert!(!platform.is_local());
    assert!(platform.is_distributed());
    assert_eq!(platform.mode(), DeploymentMode::Distributed);
    assert_eq!(platform.id(), "test_distributed_platform");
    assert_eq!(platform.node_id(), 1);
    assert_eq!(platform.peers(), &vec![2, 3]);
}

#[test]
fn test_platform_new_legacy() {
    let platform = Platform::new("legacy_platform", 1, vec![2, 3]);
    
    // new() should behave like distributed()
    assert!(!platform.is_local());
    assert!(platform.is_distributed());
    assert_eq!(platform.mode(), DeploymentMode::Distributed);
    assert_eq!(platform.id(), "legacy_platform");
    assert_eq!(platform.node_id(), 1);
    assert_eq!(platform.peers(), &vec![2, 3]);
}

// ============================================================================
// PLATFORM PROPERTIES TESTS
// ============================================================================

#[test]
fn test_platform_local_properties() {
    let platform = Platform::local("local_props_test");
    
    assert_eq!(platform.id(), "local_props_test");
    assert_eq!(platform.mode(), DeploymentMode::Local);
    assert!(platform.is_local());
    assert!(!platform.is_distributed());
    
    // Local platform should have default node_id and empty peers
    assert_eq!(platform.node_id(), 0); // or whatever default is used
    assert_eq!(platform.peers().len(), 0);
}

#[test]
fn test_platform_distributed_properties() {
    let platform = Platform::distributed("distributed_props_test", 42, vec![1, 2, 3, 4]);
    
    assert_eq!(platform.id(), "distributed_props_test");
    assert_eq!(platform.mode(), DeploymentMode::Distributed);
    assert!(!platform.is_local());
    assert!(platform.is_distributed());
    assert_eq!(platform.node_id(), 42);
    assert_eq!(platform.peers(), &vec![1, 2, 3, 4]);
}

#[test]
fn test_platform_single_node_distributed() {
    let platform = Platform::distributed("single_node", 1, vec![]);
    
    assert!(!platform.is_local());
    assert!(platform.is_distributed());
    assert_eq!(platform.node_id(), 1);
    assert_eq!(platform.peers().len(), 0);
}

// ============================================================================
// CONTEXT MANAGEMENT TESTS
// ============================================================================

#[tokio::test]
async fn test_platform_create_context() {
    let platform = Platform::local("context_creation_platform");
    
    let result = platform.create_context("test_context").await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_platform_create_multiple_contexts() {
    let platform = Platform::local("multiple_contexts_platform");
    
    // Create multiple contexts
    for i in 1..=5 {
        let context_name = format!("context_{}", i);
        let result = platform.create_context(&context_name).await;
        assert!(result.is_ok());
    }
}

#[tokio::test]
async fn test_platform_get_context() {
    let platform = Platform::local("get_context_platform");
    
    // Create context
    platform.create_context("retrievable_context").await.unwrap();
    
    // Retrieve context
    let context_id = ActorId::new("retrievable_context");
    let context = platform.get_context(&context_id).await.unwrap();
    
    assert_eq!(context.id(), &context_id);
}

#[tokio::test]
async fn test_platform_get_nonexistent_context() {
    let platform = Platform::local("nonexistent_context_platform");
    
    let nonexistent_id = ActorId::new("nonexistent_context");
    let result = platform.get_context(&nonexistent_id).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::ContextNotFound { context_id }) = result {
        assert_eq!(context_id, "nonexistent_context");
    } else {
        panic!("Expected ContextNotFound error");
    }
}

#[tokio::test]
async fn test_platform_create_context_local_vs_distributed() {
    let local_platform = Platform::local("local_context_platform");
    let distributed_platform = Platform::distributed("distributed_context_platform", 1, vec![2, 3]);
    
    // Create contexts on both platforms
    local_platform.create_context("local_context").await.unwrap();
    distributed_platform.create_context("distributed_context").await.unwrap();
    
    // Retrieve contexts
    let local_context = local_platform.get_context(&ActorId::new("local_context")).await.unwrap();
    let distributed_context = distributed_platform.get_context(&ActorId::new("distributed_context")).await.unwrap();
    
    assert_eq!(local_context.id(), &ActorId::new("local_context"));
    assert_eq!(distributed_context.id(), &ActorId::new("distributed_context"));
}

// ============================================================================
// ACTOR MANAGEMENT TESTS
// ============================================================================

#[tokio::test]
async fn test_platform_add_actor_to_context() {
    let platform = Platform::local("actor_management_platform");
    
    // Create context
    platform.create_context("actor_context").await.unwrap();
    let context_id = ActorId::new("actor_context");
    
    // Create actor
    let actor_id = ActorId::new("platform_test_actor");
    let actor = GenericActor::<PlatformTestAction>::new(actor_id.clone());
    
    // Add actor to context
    let result = platform.add_actor_to_context(&context_id, actor).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_platform_add_actor_to_nonexistent_context() {
    let platform = Platform::local("nonexistent_context_platform");
    
    let nonexistent_context_id = ActorId::new("nonexistent_context");
    let actor_id = ActorId::new("test_actor");
    let actor = GenericActor::<PlatformTestAction>::new(actor_id);
    
    let result = platform.add_actor_to_context(&nonexistent_context_id, actor).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::ContextNotFound { context_id }) = result {
        assert_eq!(context_id, "nonexistent_context");
    } else {
        panic!("Expected ContextNotFound error");
    }
}

#[tokio::test]
async fn test_platform_add_multiple_actors() {
    let platform = Platform::local("multiple_actors_platform");
    
    // Create context
    platform.create_context("multi_actor_context").await.unwrap();
    let context_id = ActorId::new("multi_actor_context");
    
    // Add multiple actors
    for i in 1..=5 {
        let actor_id = ActorId::new(&format!("actor_{}", i));
        let actor = GenericActor::<PlatformTestAction>::new(actor_id);
        
        let result = platform.add_actor_to_context(&context_id, actor).await;
        assert!(result.is_ok());
    }
}

// ============================================================================
// ACTION SENDING TESTS
// ============================================================================

#[tokio::test]
async fn test_platform_send_action() {
    let platform = Platform::local("send_action_platform");
    
    // Create context and actor
    platform.create_context("action_context").await.unwrap();
    let context_id = ActorId::new("action_context");
    
    let actor_id = ActorId::new("action_actor");
    let actor = GenericActor::<PlatformTestAction>::new(actor_id.clone());
    platform.add_actor_to_context(&context_id, actor).await.unwrap();
    
    // Send action
    let action = PlatformTestAction {
        command: "test_command".to_string(),
        value: 42,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let reaction_bytes = platform.send_action(&context_id, &actor_id, action_bytes).await.unwrap();
    
    let reaction = PlatformTestReaction::from_bytes(&reaction_bytes).unwrap();
    assert_eq!(reaction.response, "Executed command: test_command with value: 42");
    assert_eq!(reaction.processed_value, 84);
}

#[tokio::test]
async fn test_platform_send_action_to_nonexistent_context() {
    let platform = Platform::local("nonexistent_send_platform");
    
    let nonexistent_context_id = ActorId::new("nonexistent_context");
    let actor_id = ActorId::new("test_actor");
    
    let action = PlatformTestAction {
        command: "test".to_string(),
        value: 1,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let result = platform.send_action(&nonexistent_context_id, &actor_id, action_bytes).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::ContextNotFound { context_id }) = result {
        assert_eq!(context_id, "nonexistent_context");
    } else {
        panic!("Expected ContextNotFound error");
    }
}

#[tokio::test]
async fn test_platform_send_action_to_nonexistent_actor() {
    let platform = Platform::local("nonexistent_actor_send_platform");
    
    // Create context but no actor
    platform.create_context("empty_context").await.unwrap();
    let context_id = ActorId::new("empty_context");
    
    let nonexistent_actor_id = ActorId::new("nonexistent_actor");
    
    let action = PlatformTestAction {
        command: "test".to_string(),
        value: 1,
    };
    
    let action_bytes = action.to_bytes().unwrap();
    let result = platform.send_action(&context_id, &nonexistent_actor_id, action_bytes).await;
    
    assert!(result.is_err());
    if let Err(TamtilError::ActorNotFound { actor_id }) = result {
        assert_eq!(actor_id, "nonexistent_actor");
    } else {
        panic!("Expected ActorNotFound error");
    }
}

// ============================================================================
// DEPLOYMENT MODE TESTS
// ============================================================================

#[test]
fn test_deployment_mode_variants() {
    let local_mode = DeploymentMode::Local;
    let distributed_mode = DeploymentMode::Distributed;
    
    assert_ne!(local_mode, distributed_mode);
    assert_eq!(local_mode, DeploymentMode::Local);
    assert_eq!(distributed_mode, DeploymentMode::Distributed);
}

#[test]
fn test_platform_mode_consistency() {
    let local_platform = Platform::local("mode_test_local");
    let distributed_platform = Platform::distributed("mode_test_distributed", 1, vec![2]);
    
    assert_eq!(local_platform.mode(), DeploymentMode::Local);
    assert_eq!(distributed_platform.mode(), DeploymentMode::Distributed);
    
    assert!(local_platform.is_local());
    assert!(!local_platform.is_distributed());
    
    assert!(!distributed_platform.is_local());
    assert!(distributed_platform.is_distributed());
}

// ============================================================================
// EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================

#[tokio::test]
async fn test_platform_create_duplicate_context() {
    let platform = Platform::local("duplicate_context_platform");
    
    // Create context
    platform.create_context("duplicate_context").await.unwrap();
    
    // Try to create context with same name
    let result = platform.create_context("duplicate_context").await;
    
    // Should handle duplicate gracefully (implementation-dependent)
    match result {
        Ok(_) => {
            // If it succeeds, it might replace or ignore the duplicate
        }
        Err(_) => {
            // If it fails, that's also acceptable behavior
        }
    }
}

#[test]
fn test_platform_empty_name() {
    let platform = Platform::local("");
    
    assert_eq!(platform.id(), "");
    assert!(platform.is_local());
}

#[test]
fn test_platform_distributed_with_self_in_peers() {
    let platform = Platform::distributed("self_peer_platform", 1, vec![1, 2, 3]);
    
    // Should handle self in peers list gracefully
    assert_eq!(platform.node_id(), 1);
    assert_eq!(platform.peers(), &vec![1, 2, 3]);
}

// ============================================================================
// INTEGRATION SCENARIO TESTS
// ============================================================================

#[tokio::test]
async fn test_platform_full_workflow() {
    let platform = Platform::local("workflow_platform");
    
    // Create context
    platform.create_context("workflow_context").await.unwrap();
    let context_id = ActorId::new("workflow_context");
    
    // Add actor
    let actor_id = ActorId::new("workflow_actor");
    let actor = GenericActor::<PlatformTestAction>::new(actor_id.clone());
    platform.add_actor_to_context(&context_id, actor).await.unwrap();
    
    // Send multiple actions
    for i in 1..=3 {
        let action = PlatformTestAction {
            command: format!("workflow_step_{}", i),
            value: i * 10,
        };
        
        let action_bytes = action.to_bytes().unwrap();
        let reaction_bytes = platform.send_action(&context_id, &actor_id, action_bytes).await.unwrap();
        
        let reaction = PlatformTestReaction::from_bytes(&reaction_bytes).unwrap();
        assert_eq!(reaction.processed_value, i * 20);
        assert!(reaction.response.contains(&format!("workflow_step_{}", i)));
    }
}

#[tokio::test]
async fn test_platform_multi_context_workflow() {
    let platform = Platform::local("multi_context_platform");
    
    // Create multiple contexts with actors
    for i in 1..=3 {
        let context_name = format!("context_{}", i);
        let actor_name = format!("actor_{}", i);
        
        platform.create_context(&context_name).await.unwrap();
        let context_id = ActorId::new(&context_name);
        
        let actor_id = ActorId::new(&actor_name);
        let actor = GenericActor::<PlatformTestAction>::new(actor_id.clone());
        platform.add_actor_to_context(&context_id, actor).await.unwrap();
        
        // Send action to each actor
        let action = PlatformTestAction {
            command: format!("multi_context_test_{}", i),
            value: i,
        };
        
        let action_bytes = action.to_bytes().unwrap();
        let reaction_bytes = platform.send_action(&context_id, &actor_id, action_bytes).await.unwrap();
        
        let reaction = PlatformTestReaction::from_bytes(&reaction_bytes).unwrap();
        assert_eq!(reaction.processed_value, i * 2);
    }
}
