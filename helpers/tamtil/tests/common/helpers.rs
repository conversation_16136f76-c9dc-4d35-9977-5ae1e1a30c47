//! # Test Helpers
//!
//! ## Shared Test Utilities and Mock Implementations
//!
//! This module provides common utilities for creating test data, mock actors,
//! and helper functions used across multiple test modules.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// SIMPLE TEST ACTION AND REACTION
// ============================================================================

/// Simple test action for basic testing scenarios
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct SimpleTestAction {
    pub data: String,
}

#[async_trait::async_trait]
impl Action for SimpleTestAction {
    type Reaction = SimpleTestReaction;

    async fn act(&self, _memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        Ok(SimpleTestReaction {
            result: format!("Processed: {}", self.data),
        })
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize SimpleTestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize SimpleTestAction: {}", e)
            })
    }
}

/// Simple test reaction for basic testing scenarios
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct SimpleTestReaction {
    pub result: String,
}

impl Reaction for SimpleTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: "last_result".to_string(),
                value: self.result.as_bytes().to_vec(),
            },
        ]
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize SimpleTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize SimpleTestReaction: {}", e)
            })
    }
}

// ============================================================================
// COUNTER TEST ACTION AND REACTION
// ============================================================================

/// Counter test action for testing stateful operations
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CounterTestAction {
    pub operation: CounterOperation,
    pub amount: i32,
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CounterOperation {
    Increment,
    Decrement,
    Set,
    Get,
}

#[async_trait::async_trait]
impl Action for CounterTestAction {
    type Reaction = CounterTestReaction;

    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        let current_value = memories.get_counter("test_counter").await.unwrap_or(0);
        
        let new_value = match self.operation {
            CounterOperation::Increment => current_value + self.amount,
            CounterOperation::Decrement => current_value - self.amount,
            CounterOperation::Set => self.amount,
            CounterOperation::Get => current_value,
        };
        
        Ok(CounterTestReaction {
            old_value: current_value,
            new_value,
            operation: self.operation.clone(),
        })
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize CounterTestAction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize CounterTestAction: {}", e)
            })
    }
}

/// Counter test reaction for testing stateful operations
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CounterTestReaction {
    pub old_value: i32,
    pub new_value: i32,
    pub operation: CounterOperation,
}

impl Reaction for CounterTestReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        match self.operation {
            CounterOperation::Get => {
                // Get operation doesn't change state
                vec![]
            }
            _ => {
                vec![
                    MemoryOperation::Set {
                        key: "test_counter".to_string(),
                        value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.new_value).unwrap().to_vec(),
                    },
                    MemoryOperation::Increment {
                        key: "operation_count".to_string(),
                        amount: 1,
                    },
                ]
            }
        }
    }

    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize CounterTestReaction: {}", e)
            })
    }

    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize CounterTestReaction: {}", e)
            })
    }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/// Create a test platform with a default context and actor
pub async fn create_test_platform_with_actor<A: Action + 'static>(
    platform_name: &str,
    context_name: &str,
    actor_name: &str,
) -> (Platform, ActorId, ActorId) 
where
    A::Reaction: 'static,
{
    let platform = Platform::local(platform_name);
    
    // Create context
    platform.create_context(context_name).await.unwrap();
    let context_id = ActorId::new(context_name);
    
    // Create and add actor
    let actor_id = ActorId::new(actor_name);
    let actor = GenericActor::<A>::new(actor_id.clone());
    platform.add_actor_to_context(&context_id, actor).await.unwrap();
    
    (platform, context_id, actor_id)
}

/// Create a test entry for consensus testing
pub fn create_test_entry(sequence: u64, actor_name: &str, proposer: u64) -> TamtilEntry {
    TamtilEntry {
        reaction_bytes: format!("test_reaction_{}", sequence).into_bytes(),
        actor_id: ActorId::new(actor_name),
        sequence,
        timestamp: sequence * 1000, // Simple timestamp
        proposer,
    }
}

/// Create a test snapshot with given entries
pub fn create_test_snapshot(entries: &[TamtilEntry]) -> TamtilSnapshot {
    TamtilSnapshot::create(entries)
}

/// Create a test ballot
pub fn create_test_ballot(n: u64, pid: u64) -> Ballot {
    Ballot { n, pid }
}

/// Create test consensus message
pub fn create_test_prepare_req(ballot: Ballot) -> ConsensusMessage {
    ConsensusMessage::PrepareReq { ballot }
}

/// Create test promise message
pub fn create_test_promise(ballot: Ballot, entries: Vec<TamtilEntry>) -> ConsensusMessage {
    ConsensusMessage::Promise {
        ballot,
        accepted_entries: entries,
        last_accepted_ballot: Some(ballot),
    }
}

/// Create test HTTP action (GET)
pub fn create_test_http_get(params: Vec<(String, String)>) -> HttpAction {
    HttpAction::Get {
        query_params: params,
    }
}

/// Create test HTTP action (POST)
pub fn create_test_http_post(body: Vec<u8>, content_type: &str) -> HttpAction {
    HttpAction::Post {
        body,
        content_type: content_type.to_string(),
    }
}

/// Create test subscription
pub fn create_test_subscription(publisher_id: &str, reaction_type: &str) -> Subscription {
    Subscription {
        publisher_id: ActorId::new(publisher_id),
        reaction_type: reaction_type.to_string(),
    }
}

/// Create test subscribed reaction
pub fn create_test_subscribed_reaction(
    publisher_id: &str,
    reaction_bytes: Vec<u8>,
    reaction_type: &str,
) -> SubscribedReaction {
    SubscribedReaction {
        publisher_id: ActorId::new(publisher_id),
        reaction_bytes,
        reaction_type: reaction_type.to_string(),
    }
}

/// Generate test data of specified size
pub fn generate_test_data(size: usize) -> Vec<u8> {
    (0..size).map(|i| (i % 256) as u8).collect()
}

/// Create multiple test entries
pub fn create_test_entries(count: usize, start_sequence: u64) -> Vec<TamtilEntry> {
    (0..count)
        .map(|i| create_test_entry(start_sequence + i as u64, &format!("actor_{}", i), 1))
        .collect()
}

/// Assert that two byte vectors are equal (with better error messages)
pub fn assert_bytes_equal(actual: &[u8], expected: &[u8], context: &str) {
    assert_eq!(
        actual.len(),
        expected.len(),
        "{}: Length mismatch. Expected {} bytes, got {} bytes",
        context,
        expected.len(),
        actual.len()
    );
    
    for (i, (a, e)) in actual.iter().zip(expected.iter()).enumerate() {
        assert_eq!(
            a, e,
            "{}: Byte mismatch at index {}. Expected {}, got {}",
            context, i, e, a
        );
    }
}

/// Wait for a condition to become true (useful for async testing)
pub async fn wait_for_condition<F, Fut>(mut condition: F, timeout_ms: u64) -> bool
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = bool>,
{
    let start = std::time::Instant::now();
    let timeout = std::time::Duration::from_millis(timeout_ms);
    
    while start.elapsed() < timeout {
        if condition().await {
            return true;
        }
        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
    }
    
    false
}

// ============================================================================
// TEST CONSTANTS
// ============================================================================

pub const DEFAULT_TEST_TIMEOUT_MS: u64 = 5000;
pub const DEFAULT_TEST_ACTOR_NAME: &str = "test_actor";
pub const DEFAULT_TEST_CONTEXT_NAME: &str = "test_context";
pub const DEFAULT_TEST_PLATFORM_NAME: &str = "test_platform";

// ============================================================================
// ASSERTION HELPERS
// ============================================================================

/// Assert that a result is a specific TAMTIL error type
pub fn assert_tamtil_error<T>(result: TamtilResult<T>, expected_error_type: &str) {
    assert!(result.is_err(), "Expected error but got Ok");
    let error = result.unwrap_err();
    let error_string = format!("{}", error);
    assert!(
        error_string.contains(expected_error_type),
        "Expected error containing '{}', but got: {}",
        expected_error_type,
        error_string
    );
}

/// Assert that an actor ID matches expected value
pub fn assert_actor_id(actual: &ActorId, expected: &str) {
    assert_eq!(actual.id, expected, "Actor ID mismatch");
}

/// Assert that a ballot matches expected values
pub fn assert_ballot(actual: &Ballot, expected_n: u64, expected_pid: u64) {
    assert_eq!(actual.n, expected_n, "Ballot number mismatch");
    assert_eq!(actual.pid, expected_pid, "Ballot proposer ID mismatch");
}
