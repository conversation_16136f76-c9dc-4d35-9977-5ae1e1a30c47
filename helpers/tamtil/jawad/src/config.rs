//! # Configuration Management for Jawad
//!
//! ## Production-Ready Configuration
//!
//! This module provides complete configuration management for Jawad,
//! supporting both CLI and distributed web deployments with proper
//! validation, environment variable support, and configuration files.

use serde_json;
use std::env;
use std::fs;
use std::path::Path;
use tracing::{info, warn, debug};
use anyhow::{Result, Context};

// ============================================================================
// CONFIGURATION STRUCTURES
// ============================================================================

/// ## Jawad Configuration
/// 
/// ### Complete Configuration Management
/// This structure contains all configuration options for Jawad,
/// supporting both local CLI and distributed web deployments.
#[derive(Debug, Clone)]
pub struct JawadConfig {
    /// Deployment mode (local for CLI, distributed for web)
    pub deployment_mode: DeploymentMode,
    /// Node configuration
    pub node: NodeConfig,
    /// Gemini API configuration
    pub gemini: GeminiConfig,
    /// Server configuration
    pub server: ServerConfig,
    /// Logging configuration
    pub logging: LoggingConfig,
}

/// ## Deployment Mode Configuration
/// 
/// ### Why Separate Modes?
/// CLI applications need maximum performance with local execution,
/// while web applications need fault tolerance with distributed consensus.
#[derive(Debug, Clone, PartialEq)]
pub enum DeploymentMode {
    /// Local mode for CLI applications
    Local,
    /// Distributed mode for web applications
    Distributed { peers: Vec<u64> },
}

/// ## Node Configuration
/// 
/// ### Distributed System Identity
/// Each Jawad instance needs a unique identity for consensus and networking.
#[derive(Debug, Clone)]
pub struct NodeConfig {
    /// Unique node identifier
    pub node_id: u64,
    /// Node name for logging and identification
    pub name: String,
    /// Bind address for this node
    pub bind_address: String,
}

/// ## Gemini API Configuration
/// 
/// ### Production API Integration
/// Complete configuration for Google Gemini API integration
/// with proper authentication and rate limiting.
#[derive(Debug, Clone)]
pub struct GeminiConfig {
    /// Gemini API key
    pub api_key: String,
    /// API base URL
    pub base_url: String,
    /// Request timeout in seconds
    pub timeout_seconds: u64,
    /// Maximum retries for failed requests
    pub max_retries: u32,
    /// Use mock client for testing
    pub use_mock: bool,
}

/// ## Server Configuration
/// 
/// ### HTTP Server Settings
/// Configuration for the HTTP server that handles client connections.
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// Server port
    pub port: u16,
    /// Bind address
    pub host: String,
    /// Maximum concurrent connections
    pub max_connections: usize,
    /// Request timeout in seconds
    pub request_timeout_seconds: u64,
}

/// ## Logging Configuration
/// 
/// ### Observability Settings
/// Complete logging configuration for production monitoring.
#[derive(Debug, Clone)]
pub struct LoggingConfig {
    /// Log level (trace, debug, info, warn, error)
    pub level: String,
    /// Log format (json, pretty)
    pub format: String,
    /// Enable file logging
    pub file_enabled: bool,
    /// Log file path
    pub file_path: String,
}

// ============================================================================
// CONFIGURATION IMPLEMENTATION
// ============================================================================

impl JawadConfig {
    /// Create configuration for CLI mode
    /// 
    /// ### Why CLI Configuration?
    /// CLI applications need simple, fast configuration with sensible defaults
    /// for local development and single-user scenarios.
    pub fn cli() -> Self {
        Self {
            deployment_mode: DeploymentMode::Local,
            node: NodeConfig {
                node_id: 1,
                name: "jawad-cli".to_string(),
                bind_address: "127.0.0.1:0".to_string(), // Random port for CLI
            },
            gemini: GeminiConfig::default(),
            server: ServerConfig {
                port: 0, // No server needed for CLI
                host: "127.0.0.1".to_string(),
                max_connections: 1,
                request_timeout_seconds: 30,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                format: "pretty".to_string(),
                file_enabled: false,
                file_path: "".to_string(),
            },
        }
    }

    /// Create configuration for distributed web mode
    /// 
    /// ### Why Distributed Configuration?
    /// Web applications need fault tolerance, load balancing, and proper
    /// production settings for multi-user, high-availability scenarios.
    pub fn distributed(node_id: u64, peers: Vec<u64>, port: u16) -> Self {
        Self {
            deployment_mode: DeploymentMode::Distributed { peers },
            node: NodeConfig {
                node_id,
                name: format!("jawad-web-{}", node_id),
                bind_address: format!("0.0.0.0:{}", port + 1000), // Consensus port
            },
            gemini: GeminiConfig::default(),
            server: ServerConfig {
                port,
                host: "0.0.0.0".to_string(),
                max_connections: 1000,
                request_timeout_seconds: 60,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                format: "json".to_string(), // JSON for production
                file_enabled: true,
                file_path: format!("jawad-{}.log", node_id),
            },
        }
    }

    /// Load configuration from environment variables and files
    /// 
    /// ### Why Environment Configuration?
    /// Production deployments need configuration through environment variables
    /// for security (API keys) and deployment flexibility.
    pub fn from_env() -> Result<Self> {
        debug!("🔧 Loading configuration from environment");

        // Determine deployment mode
        let deployment_mode = match env::var("JAWAD_MODE").as_deref() {
            Ok("distributed") => {
                let peers_str = env::var("JAWAD_PEERS").unwrap_or_default();
                let peers = parse_peers(&peers_str)?;
                DeploymentMode::Distributed { peers }
            }
            _ => DeploymentMode::Local,
        };

        // Load node configuration
        let node_id = env::var("JAWAD_NODE_ID")
            .unwrap_or_else(|_| "1".to_string())
            .parse()
            .context("Invalid JAWAD_NODE_ID")?;

        let port = env::var("JAWAD_PORT")
            .unwrap_or_else(|_| "8080".to_string())
            .parse()
            .context("Invalid JAWAD_PORT")?;

        // Create base configuration
        let mut config = match deployment_mode {
            DeploymentMode::Local => Self::cli(),
            DeploymentMode::Distributed { peers } => Self::distributed(node_id, peers, port),
        };

        // Override with environment variables
        config.apply_env_overrides()?;

        info!("✅ Configuration loaded successfully");
        debug!("📋 Configuration: {:?}", config);

        Ok(config)
    }

    /// Apply environment variable overrides
    fn apply_env_overrides(&mut self) -> Result<()> {
        // Gemini API configuration
        if let Ok(api_key) = env::var("GEMINI_API_KEY") {
            self.gemini.api_key = api_key;
        }

        if let Ok(use_mock) = env::var("JAWAD_USE_MOCK_GEMINI") {
            self.gemini.use_mock = use_mock.parse().unwrap_or(false);
        }

        // Logging configuration
        if let Ok(log_level) = env::var("JAWAD_LOG_LEVEL") {
            self.logging.level = log_level;
        }

        if let Ok(log_format) = env::var("JAWAD_LOG_FORMAT") {
            self.logging.format = log_format;
        }

        Ok(())
    }

    /// Validate configuration
    /// 
    /// ### Why Validation?
    /// Production systems need comprehensive validation to catch
    /// configuration errors early and provide clear error messages.
    pub fn validate(&self) -> Result<()> {
        // Validate Gemini API key
        if !self.gemini.use_mock && self.gemini.api_key.is_empty() {
            return Err(anyhow::anyhow!(
                "Gemini API key is required when not using mock client. Set GEMINI_API_KEY environment variable."
            ));
        }

        // Validate node ID
        if self.node.node_id == 0 {
            return Err(anyhow::anyhow!("Node ID must be greater than 0"));
        }

        // Validate distributed mode
        if let DeploymentMode::Distributed { peers } = &self.deployment_mode {
            if peers.contains(&self.node.node_id) {
                return Err(anyhow::anyhow!("Node ID cannot be in peers list"));
            }
        }

        // Validate server configuration
        if matches!(self.deployment_mode, DeploymentMode::Distributed { .. }) {
            if self.server.port == 0 {
                return Err(anyhow::anyhow!("Server port must be specified for distributed mode"));
            }
        }

        info!("✅ Configuration validation passed");
        Ok(())
    }

    /// Get deployment mode
    pub fn is_local(&self) -> bool {
        matches!(self.deployment_mode, DeploymentMode::Local)
    }

    /// Get deployment mode
    pub fn is_distributed(&self) -> bool {
        matches!(self.deployment_mode, DeploymentMode::Distributed { .. })
    }

    /// Get peers (empty for local mode)
    pub fn peers(&self) -> Vec<u64> {
        match &self.deployment_mode {
            DeploymentMode::Distributed { peers } => peers.clone(),
            DeploymentMode::Local => Vec::new(),
        }
    }
}

impl Default for GeminiConfig {
    fn default() -> Self {
        Self {
            api_key: env::var("GEMINI_API_KEY").unwrap_or_else(|_| 
                "AIzaSyDyea4CzzDyhfzyjW7qrZz98Q08J5G-lj8".to_string()
            ),
            base_url: "https://generativelanguage.googleapis.com/v1beta".to_string(),
            timeout_seconds: 30,
            max_retries: 3,
            use_mock: env::var("JAWAD_USE_MOCK_GEMINI").map(|v| v.parse().unwrap_or(false)).unwrap_or(false),
        }
    }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/// Parse peers from comma-separated string
fn parse_peers(peers_str: &str) -> Result<Vec<u64>> {
    if peers_str.is_empty() {
        return Ok(Vec::new());
    }

    peers_str
        .split(',')
        .map(|s| s.trim().parse::<u64>().context("Invalid peer ID"))
        .collect()
}

/// Load configuration from file (if exists)
pub fn load_config_file<P: AsRef<Path>>(path: P) -> Result<Option<JawadConfig>> {
    let path = path.as_ref();
    
    if !path.exists() {
        debug!("📄 Configuration file not found: {:?}", path);
        return Ok(None);
    }

    let content = fs::read_to_string(path)
        .context("Failed to read configuration file")?;

    // For now, we'll implement a simple format
    // In production, this would support TOML, YAML, or JSON
    warn!("📄 Configuration file support not yet implemented");
    Ok(None)
}
