//! # CLI Client for Jawad
//!
//! ## Interactive Chat Interface
//!
//! This module provides a complete CLI interface for chatting with <PERSON>awa<PERSON>.
//! It includes interactive mode, single message mode, and proper terminal
//! handling with colors and formatting.

use std::io::{self, Write};
use crossterm::{
    event::{self, Event, KeyCode, KeyEvent},
    execute,
    style::{Color, Print, ResetColor, SetForegroundColor},
    terminal::{disable_raw_mode, enable_raw_mode, Clear, ClearType},
    cursor::{MoveTo, MoveToColumn},
};
use tokio::net::TcpStream;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tracing::{debug, error, info};

// ============================================================================
// CHAT CLIENT
// ============================================================================

/// ## Chat Client for Jawad
/// 
/// ### Complete CLI Interface
/// This client provides a full-featured chat interface with proper
/// terminal handling, colors, and user experience.
pub struct ChatClient {
    /// Server address
    server_address: String,
    /// User name
    user_name: String,
}

impl ChatClient {
    /// Create new chat client
    pub fn new(server_address: String, user_name: String) -> Self {
        Self {
            server_address,
            user_name,
        }
    }
    
    /// Start interactive chat session
    pub async fn start_interactive(&self) -> anyhow::Result<()> {
        // Print welcome message
        self.print_welcome().await?;
        
        // Test connection
        if let Err(e) = self.test_connection().await {
            self.print_error(&format!("❌ Failed to connect to Jawad server: {}", e)).await?;
            return Err(e);
        }
        
        self.print_success("✅ Connected to Jawad server!").await?;
        self.print_info("💡 Type your message and press Enter. Type 'quit' or 'exit' to leave.").await?;
        println!();
        
        // Start interactive loop
        loop {
            // Print prompt
            self.print_prompt().await?;
            
            // Read user input
            let input = match self.read_user_input().await {
                Ok(input) => input,
                Err(e) => {
                    self.print_error(&format!("Failed to read input: {}", e)).await?;
                    continue;
                }
            };
            
            // Check for exit commands
            let trimmed_input = input.trim().to_lowercase();
            if trimmed_input == "quit" || trimmed_input == "exit" || trimmed_input == "bye" {
                self.print_info("👋 وداعا! شكرا لك على استخدام جواد!").await?;
                break;
            }
            
            // Skip empty messages
            if input.trim().is_empty() {
                continue;
            }
            
            // Send message and get response
            match self.send_message(input).await {
                Ok(response) => {
                    self.print_jawad_response(&response).await?;
                }
                Err(e) => {
                    self.print_error(&format!("❌ Error: {}", e)).await?;
                }
            }
            
            println!(); // Add spacing
        }
        
        Ok(())
    }
    
    /// Send single message to Jawad
    pub async fn send_message(&self, message: String) -> anyhow::Result<String> {
        debug!("📤 Sending message to {}: {}", self.server_address, message);
        
        // Connect to server
        let mut stream = TcpStream::connect(&self.server_address).await
            .map_err(|e| anyhow::anyhow!("Failed to connect to server: {}", e))?;
        
        // Prepare request
        let request = format!(
            "POST /chat HTTP/1.1\r\n\
             Host: {}\r\n\
             User: {}\r\n\
             Message: {}\r\n\
             \r\n",
            self.server_address, self.user_name, message
        );
        
        // Send request
        stream.write_all(request.as_bytes()).await
            .map_err(|e| anyhow::anyhow!("Failed to send request: {}", e))?;
        
        // Read response
        let mut buffer = [0; 4096];
        let n = stream.read(&mut buffer).await
            .map_err(|e| anyhow::anyhow!("Failed to read response: {}", e))?;
        
        let response = String::from_utf8_lossy(&buffer[..n]);
        debug!("📥 Received response: {}", response);
        
        // Parse HTTP response
        if let Some(body_start) = response.find("\r\n\r\n") {
            let body = response[body_start + 4..].trim();
            Ok(body.to_string())
        } else {
            Err(anyhow::anyhow!("Invalid response format"))
        }
    }
    
    /// Test connection to server
    async fn test_connection(&self) -> anyhow::Result<()> {
        let test_message = "مرحبا".to_string();
        self.send_message(test_message).await?;
        Ok(())
    }
    
    /// Read user input from terminal
    async fn read_user_input(&self) -> anyhow::Result<String> {
        let mut input = String::new();
        io::stdin().read_line(&mut input)
            .map_err(|e| anyhow::anyhow!("Failed to read input: {}", e))?;
        Ok(input.trim().to_string())
    }
    
    /// Print welcome message
    async fn print_welcome(&self) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            Clear(ClearType::All),
            MoveTo(0, 0),
            SetForegroundColor(Color::Cyan),
            Print("🇲🇦 ═══════════════════════════════════════════════════════════════\n"),
            Print("   جواد - الذكي الاصطناعي المغربي الساخر\n"),
            Print("   Jawad - Distributed Sarcastic Moroccan Darija Chat AI\n"),
            Print("   Powered by TAMTIL Distributed Actor System\n"),
            Print("═══════════════════════════════════════════════════════════════\n"),
            ResetColor,
            Print("\n"),
        )?;
        
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Yellow),
            Print(&format!("👤 User: {}\n", self.user_name)),
            Print(&format!("🌐 Server: {}\n", self.server_address)),
            ResetColor,
            Print("\n"),
        )?;
        
        Ok(())
    }
    
    /// Print user prompt
    async fn print_prompt(&self) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Green),
            Print(&format!("{}> ", self.user_name)),
            ResetColor,
        )?;
        io::stdout().flush()?;
        Ok(())
    }
    
    /// Print Jawad's response
    async fn print_jawad_response(&self, response: &str) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Magenta),
            Print("🎭 جواد: "),
            ResetColor,
            SetForegroundColor(Color::White),
            Print(response),
            Print("\n"),
            ResetColor,
        )?;
        Ok(())
    }
    
    /// Print error message
    async fn print_error(&self, message: &str) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Red),
            Print(message),
            Print("\n"),
            ResetColor,
        )?;
        Ok(())
    }
    
    /// Print success message
    async fn print_success(&self, message: &str) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Green),
            Print(message),
            Print("\n"),
            ResetColor,
        )?;
        Ok(())
    }
    
    /// Print info message
    async fn print_info(&self, message: &str) -> anyhow::Result<()> {
        execute!(
            io::stdout(),
            SetForegroundColor(Color::Blue),
            Print(message),
            Print("\n"),
            ResetColor,
        )?;
        Ok(())
    }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/// Format message for display
pub fn format_message(user: &str, message: &str, is_jawad: bool) -> String {
    let timestamp = chrono::Utc::now().format("%H:%M:%S");
    
    if is_jawad {
        format!("[{}] 🎭 جواد: {}", timestamp, message)
    } else {
        format!("[{}] 👤 {}: {}", timestamp, user, message)
    }
}

/// Validate user input
pub fn validate_input(input: &str) -> Result<(), String> {
    if input.trim().is_empty() {
        return Err("Message cannot be empty".to_string());
    }
    
    if input.len() > 1000 {
        return Err("Message too long (max 1000 characters)".to_string());
    }
    
    Ok(())
}

/// Clean and sanitize user input
pub fn sanitize_input(input: &str) -> String {
    input
        .trim()
        .chars()
        .filter(|c| !c.is_control() || *c == '\n')
        .collect::<String>()
        .replace('\n', " ")
        .trim()
        .to_string()
}
