//! # Jawad - Production-Ready Distributed Moroccan Darija Chat AI
//!
//! ## Complete TAMTIL Production Demonstration
//!
//! Jawad demonstrates TAMTIL's production readiness through a real-world distributed
//! chat AI application. This implementation showcases the unified CLI/Web development
//! experience, fault tolerance, and zero-copy performance that TAMTIL provides.
//!
//! ### Production Features
//! - **Unified Development**: Same code for CLI and distributed web deployment
//! - **Fault Tolerance**: 3-node Byzantine fault tolerance with OmniPaxos consensus
//! - **Real AI Integration**: Google Gemini API with proper error handling
//! - **Event Sourcing**: Complete audit trails and state recovery
//! - **Zero-Copy Performance**: rkyv serialization throughout
//! - **Production Monitoring**: Comprehensive logging and metrics
//! - **Configuration Management**: Environment variables and validation
//! - **Moroccan Personality**: Authentic Darija responses with cultural context

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use clap::{Parser, Subcommand};
use tokio;
use tracing::{info, error, debug};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

mod jawad_actor;
mod gemini_client;
mod cli_client;
mod config;

use jawad_actor::*;
use gemini_client::*;
use cli_client::*;
use config::*;

// ============================================================================
// CLI INTERFACE
// ============================================================================

#[derive(Parser)]
#[command(name = "jawad")]
#[command(about = "Jawad - Distributed Sarcastic Moroccan Darija Chat AI")]
#[command(version = "1.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Start Jawad in CLI mode (local, single-user)
    Cli {
        /// User name
        #[arg(short, long, default_value = "Anonymous")]
        user: String,

        /// Use mock Gemini client for testing
        #[arg(long)]
        mock: bool,
    },

    /// Start Jawad server node (distributed, multi-user)
    Server {
        /// Node ID (1, 2, or 3 for fault tolerance)
        #[arg(short, long, default_value = "1")]
        node_id: u64,

        /// Peer node IDs (comma-separated)
        #[arg(short, long, default_value = "2,3")]
        peers: String,

        /// Server port
        #[arg(long, default_value = "8080")]
        port: u16,

        /// Use mock Gemini client for testing
        #[arg(long)]
        mock: bool,
    },

    /// Connect to distributed Jawad server
    Connect {
        /// Server address
        #[arg(short, long, default_value = "127.0.0.1:8080")]
        server: String,

        /// User name
        #[arg(short, long, default_value = "Anonymous")]
        user: String,
    },

    /// Send single message to distributed Jawad
    Message {
        /// Server address
        #[arg(short, long, default_value = "127.0.0.1:8080")]
        server: String,

        /// User name
        #[arg(short, long, default_value = "Anonymous")]
        user: String,

        /// Message to send
        message: String,
    },
}

// ============================================================================
// MAIN APPLICATION
// ============================================================================

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("jawad=debug,tamtil=info")
        .init();

    let cli = Cli::parse();

    match cli.command {
        Commands::Cli { user, mock } => {
            start_cli_mode(user, mock).await?;
        }
        Commands::Server { node_id, peers, port, mock } => {
            start_server_mode(node_id, peers, port, mock).await?;
        }
        Commands::Connect { server, user } => {
            start_chat_client(server, user).await?;
        }
        Commands::Message { server, user, message } => {
            send_single_message(server, user, message).await?;
        }
    }

    Ok(())
}

// ============================================================================
// CLI MODE IMPLEMENTATION
// ============================================================================

/// Start Jawad in CLI mode (local, single-user)
///
/// ### Why CLI Mode?
/// CLI mode provides maximum performance for single-user scenarios by using
/// TAMTIL's local platform without distributed consensus overhead.
async fn start_cli_mode(user: String, use_mock: bool) -> Result<(), Box<dyn std::error::Error>> {
    println!("🎭 جواد - Jawad CLI Mode");
    println!("======================");
    println!("💬 Local chat with Jawad (type 'exit' to quit)");
    println!("🚀 Using TAMTIL local platform for maximum performance");

    // Create local configuration
    let mut config = config::JawadConfig::cli();
    if use_mock {
        config.gemini.use_mock = true;
    }
    config.validate()?;

    // Create local TAMTIL platform (no consensus overhead)
    let platform = Platform::local("jawad_cli");

    // Create context for Jawad actor
    platform.create_context("chat_context").await?;
    let context_id = ActorId::new("chat_context");

    // Create Gemini client
    let gemini_client = if config.gemini.use_mock {
        GeminiClient::mock()
    } else {
        GeminiClient::new(config.gemini.api_key.clone(), config.gemini.base_url.clone())?
    };

    // Create and spawn Jawad actor
    let jawad_actor = JawadActor::new(ActorId::new("jawad"), gemini_client);
    platform.add_actor_to_context(&context_id, jawad_actor).await?;

    println!("✅ Jawad actor spawned in local mode");
    println!("💡 Same actor code as distributed mode - that's TAMTIL!");
    println!();

    // Start interactive chat loop
    let session_id = uuid::Uuid::new_v4().to_string();
    let stdin = std::io::stdin();

    loop {
        print!("{}> ", user);
        std::io::Write::flush(&mut std::io::stdout())?;

        let mut input = String::new();
        stdin.read_line(&mut input)?;
        let message = input.trim();

        if message.is_empty() {
            continue;
        }

        if message == "exit" || message == "quit" {
            println!("👋 مع السلامة! (Goodbye!)");
            break;
        }

        // Create chat action
        let action = ChatAction {
            session_id: session_id.clone(),
            user_name: user.clone(),
            message: message.to_string(),
            timestamp: chrono::Utc::now(),
        };

        // Send to Jawad actor (same API as distributed mode!)
        match platform.send_action(&context_id, &ActorId::new("jawad"), action.to_bytes()?).await {
            Ok(reaction_bytes) => {
                let reaction = ChatReaction::from_bytes(&reaction_bytes)?;
                println!("🎭 جواد: {}", reaction.jawad_response);
                println!();
            }
            Err(e) => {
                println!("❌ Error: {}", e);
            }
        }
    }

    Ok(())
}

// ============================================================================
// SERVER MODE IMPLEMENTATION
// ============================================================================

/// Start Jawad server with distributed consensus
///
/// ### Why Server Mode?
/// Server mode provides fault tolerance and multi-user support using
/// TAMTIL's distributed platform with full consensus protocol.
async fn start_server_mode(node_id: u64, peers_str: String, port: u16, use_mock: bool) -> Result<(), Box<dyn std::error::Error>> {
    info!("🚀 Starting Jawad server node {} on port {}", node_id, port);

    // Parse peer node IDs
    let peers: Vec<u64> = peers_str
        .split(',')
        .filter_map(|s| s.trim().parse().ok())
        .filter(|&id| id != node_id)
        .collect();

    info!("👥 Connecting to peer nodes: {:?}", peers);

    // Create distributed configuration
    let mut config = config::JawadConfig::distributed(node_id, peers.clone(), port);
    if use_mock {
        config.gemini.use_mock = true;
    }
    config.validate()?;

    // Create TAMTIL distributed platform with fault tolerance
    let platform = Platform::distributed(
        format!("jawad_platform_{}", node_id),
        node_id,
        peers
    );

    // Create Jawad context
    platform.create_context("jawad_context").await?;

    let context_id = ActorId::new("jawad_context");

    // Create Gemini client
    let gemini_client = if config.gemini.use_mock {
        GeminiClient::mock()
    } else {
        GeminiClient::new(config.gemini.api_key.clone(), config.gemini.base_url.clone())?
    };

    // Create Jawad actor
    let jawad_actor = JawadActor::new(
        ActorId::new("jawad"),
        gemini_client
    );

    platform.add_actor_to_context(&context_id, jawad_actor).await?;

    info!("✅ Jawad actor spawned in distributed mode");
    info!("💡 Same actor code as CLI mode - that's TAMTIL!");

    // Start HTTP server for client connections
    start_http_server(platform, context_id, port).await?;

    Ok(())
}

/// Start HTTP server for client connections
async fn start_http_server(
    platform: Platform,
    context_id: ActorId,
    port: u16
) -> anyhow::Result<()> {
    use std::sync::Arc;
    use tokio::net::TcpListener;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};

    let platform = Arc::new(platform);
    let context_id = Arc::new(context_id);

    let listener = TcpListener::bind(format!("0.0.0.0:{}", port)).await?;
    info!("🌐 HTTP server listening on port {}", port);

    loop {
        let (mut socket, addr) = listener.accept().await?;
        let platform = Arc::clone(&platform);
        let context_id = Arc::clone(&context_id);

        tokio::spawn(async move {
            let mut buffer = [0; 4096];

            match socket.read(&mut buffer).await {
                Ok(n) if n > 0 => {
                    let request = String::from_utf8_lossy(&buffer[..n]);
                    debug!("📨 Received request from {}: {}", addr, request.trim());

                    // Parse simple HTTP-like request
                    if let Some(message_line) = request.lines().find(|line| line.starts_with("Message:")) {
                        let message = message_line.strip_prefix("Message:").unwrap_or("").trim();
                        let user = request.lines()
                            .find(|line| line.starts_with("User:"))
                            .and_then(|line| line.strip_prefix("User:"))
                            .unwrap_or("Anonymous")
                            .trim();

                        // Create chat action
                        let chat_action = ChatAction {
                            session_id: Uuid::new_v4().to_string(),
                            user_name: user.to_string(),
                            message: message.to_string(),
                            timestamp: Utc::now(),
                        };

                        // Send to Jawad actor through TAMTIL
                        match chat_action.to_bytes() {
                            Ok(action_bytes) => {
                                match platform.send_action(
                                    &context_id,
                                    &ActorId::new("jawad"),
                                    action_bytes
                                ).await {
                                    Ok(reaction_bytes) => {
                                        match ChatReaction::from_bytes(&reaction_bytes) {
                                            Ok(reaction) => {
                                                let response = format!(
                                                    "HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=utf-8\r\n\r\n{}\r\n",
                                                    reaction.jawad_response
                                                );
                                                let _ = socket.write_all(response.as_bytes()).await;
                                            }
                                            Err(e) => {
                                                error!("Failed to deserialize reaction: {}", e);
                                                let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nDeserialization error\r\n";
                                                let _ = socket.write_all(response.as_bytes()).await;
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        error!("Failed to send action to Jawad: {}", e);
                                        let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nActor error\r\n";
                                        let _ = socket.write_all(response.as_bytes()).await;
                                    }
                                }
                            }
                            Err(e) => {
                                error!("Failed to serialize action: {}", e);
                                let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nSerialization error\r\n";
                                let _ = socket.write_all(response.as_bytes()).await;
                            }
                        }
                    } else {
                        let response = "HTTP/1.1 400 Bad Request\r\n\r\nInvalid request format\r\n";
                        let _ = socket.write_all(response.as_bytes()).await;
                    }
                }
                Ok(_) => {
                    debug!("Empty request from {}", addr);
                }
                Err(e) => {
                    error!("Failed to read from socket {}: {}", addr, e);
                }
            }
        });
    }
}

/// Start interactive chat client
async fn start_chat_client(server: String, user: String) -> anyhow::Result<()> {
    let client = ChatClient::new(server, user);
    client.start_interactive().await
}

/// Send single message and exit
async fn send_single_message(server: String, user: String, message: String) -> anyhow::Result<()> {
    let client = ChatClient::new(server, user);
    let response = client.send_message(message).await?;
    println!("{}", response);
    Ok(())
}
