//! # HTTP Types Module
//!
//! ## HTTP Action Types and Serialization
//!
//! This module defines the structure of HTTP actions that actors can process.
//! It provides simple GET/POST mapping that abstracts HTTP complexity from
//! developers while maintaining full functionality.

use crate::common_types::{ActorId, TamtilError, TamtilResult};
use rkyv::{Archive, Serialize, Deserialize};
use std::collections::HashMap;

/// ## HTTP Action Types
///
/// ### Simple GET/POST Mapping
/// GET actions contain no data, POST actions contain data.
/// This abstracts HTTP complexity from developers.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum HttpAction {
    /// GET request - no data
    Get {
        /// Target actor ID
        actor_id: ActorId,
        /// Query parameters as key-value pairs
        params: HashMap<String, String>,
    },
    /// POST request - with data
    Post {
        /// Target actor ID
        actor_id: ActorId,
        /// Request body data
        data: Vec<u8>,
        /// Content type
        content_type: String,
    },
}

impl HttpAction {
    /// Serialize HTTP action to bytes
    pub fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize HTTP action: {}", e)
            })
    }

    /// Deserialize HTTP action from bytes
    pub fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize HTTP action: {}", e)
            })
    }

    /// Get the target actor ID
    pub fn actor_id(&self) -> &ActorId {
        match self {
            HttpAction::Get { actor_id, .. } => actor_id,
            HttpAction::Post { actor_id, .. } => actor_id,
        }
    }

    /// Check if this is a GET request
    pub fn is_get(&self) -> bool {
        matches!(self, HttpAction::Get { .. })
    }

    /// Check if this is a POST request
    pub fn is_post(&self) -> bool {
        matches!(self, HttpAction::Post { .. })
    }

    /// Get query parameters (only for GET requests)
    pub fn get_params(&self) -> Option<&HashMap<String, String>> {
        match self {
            HttpAction::Get { params, .. } => Some(params),
            HttpAction::Post { .. } => None,
        }
    }

    /// Get request data (only for POST requests)
    pub fn get_data(&self) -> Option<&[u8]> {
        match self {
            HttpAction::Get { .. } => None,
            HttpAction::Post { data, .. } => Some(data),
        }
    }

    /// Get content type (only for POST requests)
    pub fn get_content_type(&self) -> Option<&str> {
        match self {
            HttpAction::Get { .. } => None,
            HttpAction::Post { content_type, .. } => Some(content_type),
        }
    }
}
