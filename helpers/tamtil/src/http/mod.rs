//! # HTTP Module
//!
//! ## HTTP Server Integration for TAMTIL
//!
//! This module provides HTTP server integration for TAMTIL, enabling
//! web applications to interact with actors through simple GET/POST
//! requests. HTTP complexity is completely hidden from developers.
//!
//! ### Sub-modules
//! - `types`: HTTP action types and serialization
//! - `server`: HTTP server implementation and request handling
//!
//! ### Why HTTP Integration?
//! HTTP provides a universal interface for web applications to interact
//! with TAMTIL actors. The simple GET/POST mapping abstracts HTTP
//! complexity while maintaining full actor system capabilities.

pub mod types;
pub mod server;

// Re-export all public types and functions
pub use types::*;
pub use server::*;
