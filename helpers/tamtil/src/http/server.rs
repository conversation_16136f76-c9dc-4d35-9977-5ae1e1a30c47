//! # HTTP Server Module
//!
//! ## HTTP Server Implementation and Request Handling
//!
//! This module implements the HTTP server logic for TAMTIL, providing
//! simple GET/POST interface that maps to actor actions. HTTP complexity
//! is completely hidden from developers.

use crate::common_types::{ActorId, Tam<PERSON>Error, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::http::types::HttpAction;
use crate::platform::context::Context;
use bytes;
use http_body_util::{BodyExt, Full};
use hyper::body::Incoming;
use hyper::service::service_fn;
use hyper::{Method, Request, Response, StatusCode};
use hyper_util::rt::TokioIo;
use hyper_util::server::conn::auto::Builder;
use matchit::Router;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::TcpListener;
use tracing::{debug, error, info};

/// ## HTTP Server for TAMTIL
///
/// ### Complete HTTP2/HTTP3 Integration
/// Provides simple GET/POST interface that maps to actor actions.
/// HTTP complexity is completely hidden from developers.
pub struct HttpServer {
    /// Server address
    address: SocketAddr,
    /// URL router for mapping paths to actors
    router: Router<ActorId>,
    /// Reference to context for actor communication
    context: Arc<Context>,
}

impl HttpServer {
    /// Create new HTTP server
    pub fn new(address: SocketAddr, context: Arc<Context>) -> Self {
        Self {
            address,
            router: Router::new(),
            context,
        }
    }

    /// Add route mapping URL path to actor
    pub fn add_route(&mut self, path: &str, actor_id: ActorId) -> TamtilResult<()> {
        self.router.insert(path, actor_id)
            .map_err(|e| TamtilError::Validation {
                message: format!("Failed to add route {}: {}", path, e)
            })?;
        Ok(())
    }

    /// Start HTTP server
    pub async fn start(self) -> TamtilResult<()> {
        let context = self.context;
        let router = Arc::new(self.router);

        let listener = TcpListener::bind(self.address).await
            .map_err(|e| TamtilError::Network {
                message: format!("Failed to bind to {}: {}", self.address, e)
            })?;

        info!("🌐 HTTP server listening on {}", self.address);

        loop {
            let (stream, _) = listener.accept().await
                .map_err(|e| TamtilError::Network {
                    message: format!("Failed to accept connection: {}", e)
                })?;

            let io = TokioIo::new(stream);
            let context = Arc::clone(&context);
            let router = Arc::clone(&router);

            tokio::task::spawn(async move {
                let service = service_fn(move |req| {
                    let context = Arc::clone(&context);
                    let router = Arc::clone(&router);
                    handle_http_request(req, context, router)
                });

                if let Err(err) = Builder::new(hyper_util::rt::TokioExecutor::new())
                    .serve_connection(io, service)
                    .await
                {
                    error!("Error serving connection: {:?}", err);
                }
            });
        }
    }
}

/// Handle HTTP request and route to appropriate actor
pub async fn handle_http_request(
    req: Request<Incoming>,
    context: Arc<Context>,
    router: Arc<Router<ActorId>>,
) -> Result<Response<Full<bytes::Bytes>>, Box<dyn std::error::Error + Send + Sync>> {
    let method = req.method().clone();
    let uri = req.uri().clone();
    let path = uri.path();

    debug!("📨 HTTP {} {}", method, path);

    // Route to actor
    let actor_id = match router.at(path) {
        Ok(matched) => matched.value.clone(),
        Err(_) => {
            return Ok(Response::builder()
                .status(StatusCode::NOT_FOUND)
                .body(Full::new(bytes::Bytes::from("Actor not found")))
                .unwrap());
        }
    };

    // Create HTTP action based on method
    let http_action = match method {
        Method::GET => {
            // Parse query parameters
            let params = uri.query()
                .map(|q| {
                    url::form_urlencoded::parse(q.as_bytes())
                        .into_owned()
                        .collect::<HashMap<String, String>>()
                })
                .unwrap_or_default();

            HttpAction::Get { actor_id: actor_id.clone(), params }
        }
        Method::POST => {
            // Extract content type before consuming request
            let content_type = req.headers()
                .get("content-type")
                .and_then(|v| v.to_str().ok())
                .unwrap_or("application/octet-stream")
                .to_string();

            // Read request body
            let body_bytes = match req.into_body().collect().await {
                Ok(collected) => collected.to_bytes().to_vec(),
                Err(e) => {
                    return Ok(Response::builder()
                        .status(StatusCode::BAD_REQUEST)
                        .body(Full::new(bytes::Bytes::from(format!("Failed to read body: {}", e))))
                        .unwrap());
                }
            };

            HttpAction::Post {
                actor_id: actor_id.clone(),
                data: body_bytes,
                content_type
            }
        }
        _ => {
            return Ok(Response::builder()
                .status(StatusCode::METHOD_NOT_ALLOWED)
                .body(Full::new(bytes::Bytes::from("Only GET and POST methods are supported")))
                .unwrap());
        }
    };

    // Serialize HTTP action
    let action_bytes = match http_action.to_bytes() {
        Ok(bytes) => bytes,
        Err(e) => {
            return Ok(Response::builder()
                .status(StatusCode::INTERNAL_SERVER_ERROR)
                .body(Full::new(bytes::Bytes::from(format!("Serialization error: {}", e))))
                .unwrap());
        }
    };

    // Send to actor
    match context.send_action(&actor_id, action_bytes).await {
        Ok(reaction_bytes) => {
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header("content-type", "application/octet-stream")
                .body(Full::new(bytes::Bytes::from(reaction_bytes)))
                .unwrap())
        }
        Err(e) => {
            error!("❌ Actor error: {}", e);
            Ok(Response::builder()
                .status(StatusCode::INTERNAL_SERVER_ERROR)
                .body(Full::new(bytes::Bytes::from(format!("Actor error: {}", e))))
                .unwrap())
        }
    }
}
