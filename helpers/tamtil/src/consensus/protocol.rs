//! # Consensus Protocol Module
//!
//! ## Multi-Paxos Consensus Implementation
//!
//! This module contains the core logic of the Multi-Paxos consensus algorithm,
//! adapted from OmniPaxos SequencePaxos for TAMTIL's requirements. It implements
//! the full consensus protocol with all phases: leader election, log replication,
//! and decision notification.

use crate::common_types::{NodeId, ActorId, Ballot, TamtilResult};
use crate::consensus::types::{TamtilEntry, ConsensusMessage, Role, Phase};
use crate::consensus::storage::{TamtilStorage, StorageOp};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{info, error, debug};

/// ## Consensus Manager (Adapted from OmniPaxos SequencePaxos)
///
/// ### Complete Multi-Paxos Implementation
/// This implements the full Multi-Paxos protocol with all phases:
/// leader election, log replication, and decision notification.
pub struct TamtilConsensus {
    /// Node identifier
    node_id: NodeId,
    /// Peer node identifiers
    peers: Vec<NodeId>,
    /// Current role and phase
    state: (Role, Phase),
    /// Storage backend
    storage: TamtilStorage,
    /// Current ballot
    current_ballot: Ballot,
    /// Sequence counter
    sequence_counter: u64,
    /// Buffered entries during prepare phase
    buffered_entries: Vec<TamtilEntry>,
    /// Outgoing messages
    outgoing_messages: Vec<(NodeId, ConsensusMessage)>,
    /// Promise responses received
    promise_responses: HashMap<NodeId, (Ballot, Vec<TamtilEntry>)>,
    /// Accept responses received
    accept_responses: HashMap<NodeId, (Ballot, usize, usize)>,
}

impl TamtilConsensus {
    /// Create new consensus instance
    pub fn new(node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self {
            node_id,
            peers,
            state: (Role::Follower, Phase::None),
            storage: TamtilStorage::default(),
            current_ballot: Ballot { n: 0, pid: node_id },
            sequence_counter: 0,
            buffered_entries: Vec::new(),
            outgoing_messages: Vec::new(),
            promise_responses: HashMap::new(),
            accept_responses: HashMap::new(),
        }
    }

    /// Propose new entry (implements OmniPaxos append)
    pub fn propose_entry(&mut self, reaction_bytes: Vec<u8>, actor_id: ActorId) -> TamtilResult<()> {
        self.sequence_counter += 1;

        let entry = TamtilEntry {
            reaction_bytes,
            actor_id,
            sequence: self.sequence_counter,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            proposer: self.node_id,
        };

        match self.state {
            (Role::Leader, Phase::Accept) => {
                // Directly accept as leader
                self.accept_entry(entry)?;
            }
            (Role::Leader, Phase::Prepare) => {
                // Buffer during prepare phase
                self.buffered_entries.push(entry);
            }
            _ => {
                // Forward to leader or buffer
                self.buffered_entries.push(entry);
                self.try_become_leader()?;
            }
        }

        Ok(())
    }

    /// Try to become leader (implements OmniPaxos leader election)
    fn try_become_leader(&mut self) -> TamtilResult<()> {
        // Increment ballot number
        self.current_ballot.n += 1;
        self.current_ballot.pid = self.node_id;

        // Update state
        self.state = (Role::Leader, Phase::Prepare);

        // Send prepare requests to all peers
        for &peer in &self.peers {
            let msg = ConsensusMessage::PrepareReq {
                ballot: self.current_ballot
            };
            self.outgoing_messages.push((peer, msg));
        }

        // Store promise
        self.storage.write_atomically(vec![
            StorageOp::SetPromise(self.current_ballot)
        ])?;

        info!("Node {} starting leader election with ballot {:?}",
              self.node_id, self.current_ballot);

        Ok(())
    }

    /// Accept entry as leader
    fn accept_entry(&mut self, entry: TamtilEntry) -> TamtilResult<()> {
        // Add to storage
        self.storage.write_atomically(vec![
            StorageOp::AppendEntry(entry.clone())
        ])?;

        let first_idx = self.storage.get_log_len() - 1;

        // Send accept messages to all peers
        for &peer in &self.peers {
            let msg = ConsensusMessage::Accept {
                ballot: self.current_ballot,
                entries: vec![entry.clone()],
                first_idx,
            };
            self.outgoing_messages.push((peer, msg));
        }

        debug!("Leader {} accepting entry at index {}", self.node_id, first_idx);
        Ok(())
    }

    /// Handle incoming consensus message
    pub fn handle_message(&mut self, from: NodeId, message: ConsensusMessage) -> TamtilResult<()> {
        match message {
            ConsensusMessage::PrepareReq { ballot } => {
                self.handle_prepare_req(from, ballot)?;
            }
            ConsensusMessage::Promise { ballot, accepted_entries, last_accepted_ballot } => {
                self.handle_promise(from, ballot, accepted_entries, last_accepted_ballot)?;
            }
            ConsensusMessage::Accept { ballot, entries, first_idx } => {
                self.handle_accept(from, ballot, entries, first_idx)?;
            }
            ConsensusMessage::Accepted { ballot, first_idx, length } => {
                self.handle_accepted(from, ballot, first_idx, length)?;
            }
            ConsensusMessage::Decide { ballot, decided_idx } => {
                self.handle_decide(from, ballot, decided_idx)?;
            }
            ConsensusMessage::Heartbeat { ballot } => {
                self.handle_heartbeat(from, ballot)?;
            }
        }
        Ok(())
    }

    /// Handle prepare request
    fn handle_prepare_req(&mut self, from: NodeId, ballot: Ballot) -> TamtilResult<()> {
        let current_promise = self.storage.get_promise();

        if current_promise.map_or(true, |p| ballot > p) {
            // Promise this ballot
            self.storage.write_atomically(vec![
                StorageOp::SetPromise(ballot)
            ])?;

            // Send promise with accepted entries
            let accepted_entries = self.storage.get_entries(0, self.storage.get_log_len())?;
            let last_accepted_ballot = self.storage.get_accepted_round();

            let msg = ConsensusMessage::Promise {
                ballot,
                accepted_entries,
                last_accepted_ballot,
            };

            self.outgoing_messages.push((from, msg));

            debug!("Node {} promised ballot {:?} to node {}",
                   self.node_id, ballot, from);
        }

        Ok(())
    }

    /// Handle promise response
    fn handle_promise(&mut self, from: NodeId, ballot: Ballot,
                     accepted_entries: Vec<TamtilEntry>,
                     _last_accepted_ballot: Option<Ballot>) -> TamtilResult<()> {
        if self.state != (Role::Leader, Phase::Prepare) || ballot != self.current_ballot {
            return Ok(());
        }

        // Store promise response
        self.promise_responses.insert(from, (ballot, accepted_entries));

        // Check if we have majority
        let majority = (self.peers.len() + 1) / 2 + 1;
        if self.promise_responses.len() >= majority {
            // Become leader in accept phase
            self.state = (Role::Leader, Phase::Accept);

            // Process buffered entries
            let buffered = std::mem::take(&mut self.buffered_entries);
            for entry in buffered {
                self.accept_entry(entry)?;
            }

            info!("Node {} became leader with ballot {:?}",
                  self.node_id, self.current_ballot);
        }

        Ok(())
    }

    /// Get decided entries
    pub fn get_decided_entries(&self) -> TamtilResult<Vec<TamtilEntry>> {
        let decided_idx = self.storage.get_decided_idx();
        self.storage.get_entries(0, decided_idx)
    }

    /// Take outgoing messages
    pub fn take_outgoing_messages(&mut self) -> Vec<(NodeId, ConsensusMessage)> {
        std::mem::take(&mut self.outgoing_messages)
    }

    /// Get current state
    pub fn get_state(&self) -> (Role, Phase) {
        self.state
    }

    /// Get current ballot
    pub fn get_ballot(&self) -> Ballot {
        self.current_ballot
    }

    /// Handle accept request
    fn handle_accept(&mut self, from: NodeId, ballot: Ballot,
                    entries: Vec<TamtilEntry>, first_idx: usize) -> TamtilResult<()> {
        let current_promise = self.storage.get_promise();

        if current_promise.map_or(false, |p| ballot >= p) {
            // Accept the entries
            self.storage.write_atomically(vec![
                StorageOp::AppendEntries(entries.clone()),
                StorageOp::SetAcceptedRound(ballot),
            ])?;

            // Send accepted response
            let msg = ConsensusMessage::Accepted {
                ballot,
                first_idx,
                length: entries.len(),
            };

            self.outgoing_messages.push((from, msg));

            debug!("Node {} accepted {} entries from node {} at index {}",
                   self.node_id, entries.len(), from, first_idx);
        }

        Ok(())
    }

    /// Handle accepted response
    fn handle_accepted(&mut self, from: NodeId, ballot: Ballot,
                      first_idx: usize, length: usize) -> TamtilResult<()> {
        if self.state.0 != Role::Leader || ballot != self.current_ballot {
            return Ok(());
        }

        // Store accepted response
        self.accept_responses.insert(from, (ballot, first_idx, length));

        // Check if we have majority
        let majority = (self.peers.len() + 1) / 2 + 1;
        if self.accept_responses.len() >= majority {
            // Decide the entries
            let decided_idx = first_idx + length;

            self.storage.write_atomically(vec![
                StorageOp::SetDecidedIndex(decided_idx)
            ])?;

            // Send decide messages
            for &peer in &self.peers {
                let msg = ConsensusMessage::Decide {
                    ballot: self.current_ballot,
                    decided_idx,
                };
                self.outgoing_messages.push((peer, msg));
            }

            // Clear responses for next round
            self.accept_responses.clear();

            info!("Node {} decided entries up to index {}",
                  self.node_id, decided_idx);
        }

        Ok(())
    }

    /// Handle decide message
    fn handle_decide(&mut self, _from: NodeId, _ballot: Ballot, decided_idx: usize) -> TamtilResult<()> {
        // Update decided index
        self.storage.write_atomically(vec![
            StorageOp::SetDecidedIndex(decided_idx)
        ])?;

        debug!("Node {} updated decided index to {}", self.node_id, decided_idx);
        Ok(())
    }

    /// Handle heartbeat
    fn handle_heartbeat(&mut self, _from: NodeId, _ballot: Ballot) -> TamtilResult<()> {
        // Simple heartbeat handling - in production would update leader liveness
        Ok(())
    }
}
