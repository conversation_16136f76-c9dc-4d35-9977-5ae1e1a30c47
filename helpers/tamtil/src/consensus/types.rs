//! # Consensus Types Module
//!
//! ## Consensus-Specific Data Structures
//!
//! This module defines data structures specifically used by the consensus
//! protocol including log entries, snapshots, internal states, and messages.
//! These types are adapted from OmniPaxos for rkyv zero-copy serialization.

use crate::common_types::{NodeId, ActorId, Ballot};
use rkyv::{Archive, Serialize, Deserialize};
use std::time::{SystemTime, UNIX_EPOCH};

/// ## Consensus Entry (Adapted from OmniPaxos Entry trait)
/// 
/// ### Why Reactions as Consensus Units?
/// Unlike raw commands, reactions represent completed business logic with
/// semantic meaning, providing better consistency and natural audit trails.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct TamtilEntry {
    /// Serialized reaction data (rkyv format)
    pub reaction_bytes: Vec<u8>,
    /// Source actor identifier
    pub actor_id: ActorId,
    /// Sequence number for total ordering
    pub sequence: u64,
    /// Timestamp for conflict resolution
    pub timestamp: u64,
    /// Proposer node ID
    pub proposer: NodeId,
}

/// ## Snapshot Implementation (Adapted from OmniPaxos Snapshot trait)
/// 
/// ### Complete State Snapshots
/// We implement complete snapshots rather than delta snapshots for simplicity
/// and predictable memory usage in production environments.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct TamtilSnapshot {
    /// Complete serialized state
    pub state_data: Vec<u8>,
    /// Snapshot creation timestamp
    pub timestamp: u64,
    /// Last included sequence number
    pub last_sequence: u64,
}

impl TamtilSnapshot {
    /// Create snapshot from entries (implements Snapshot::create)
    pub fn create(entries: &[TamtilEntry]) -> Self {
        let state_data = rkyv::to_bytes::<rkyv::rancor::Error>(&entries.to_vec())
            .expect("Failed to serialize entries for snapshot")
            .to_vec();
        
        Self {
            state_data,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            last_sequence: entries.last().map(|e| e.sequence).unwrap_or(0),
        }
    }
    
    /// Merge delta snapshot (implements Snapshot::merge)
    pub fn merge(&mut self, delta: Self) {
        // For complete snapshots, we replace with the newer one
        if delta.timestamp > self.timestamp {
            *self = delta;
        }
    }
    
    /// Whether snapshots are enabled (implements Snapshot::use_snapshots)
    pub fn use_snapshots() -> bool {
        true
    }
}

/// ## Consensus Roles (Copied from OmniPaxos)
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Role {
    Leader,
    Follower,
}

/// ## Consensus Phases (Copied from OmniPaxos)
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Phase {
    Prepare,
    Accept,
    Recover,
    None,
}

/// ## Consensus Messages (Adapted from OmniPaxos)
///
/// ### Complete Message Types
/// All message types required for Multi-Paxos consensus protocol.
/// No simplified or stub message handling.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum ConsensusMessage {
    /// Prepare request for leader election
    PrepareReq { ballot: Ballot },
    /// Promise response to prepare
    Promise {
        ballot: Ballot,
        accepted_entries: Vec<TamtilEntry>,
        last_accepted_ballot: Option<Ballot>,
    },
    /// Accept request for log entries
    Accept {
        ballot: Ballot,
        entries: Vec<TamtilEntry>,
        first_idx: usize,
    },
    /// Accepted response
    Accepted {
        ballot: Ballot,
        first_idx: usize,
        length: usize,
    },
    /// Decide message for committed entries
    Decide {
        ballot: Ballot,
        decided_idx: usize,
    },
    /// Heartbeat for leader liveness
    Heartbeat { ballot: Ballot },
}
