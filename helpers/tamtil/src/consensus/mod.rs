//! # Consensus Module
//!
//! ## OmniPaxos-Based Consensus Implementation
//!
//! This module implements the consensus protocol for TAMTIL, adapted from
//! OmniPaxos algorithms for rkyv zero-copy serialization. It provides
//! distributed agreement on the order of reactions across multiple nodes.
//!
//! ### Sub-modules
//! - `types`: Consensus-specific data structures
//! - `storage`: Storage layer for consensus state
//! - `protocol`: Core consensus protocol implementation
//!
//! ### Why Consensus?
//! Consensus ensures that all nodes in a distributed TAMTIL cluster agree
//! on the order of reactions, providing strong consistency guarantees for
//! actor state across the network.

pub mod types;
pub mod storage;
pub mod protocol;

// Re-export all public types and functions
pub use types::*;
pub use storage::*;
pub use protocol::*;
