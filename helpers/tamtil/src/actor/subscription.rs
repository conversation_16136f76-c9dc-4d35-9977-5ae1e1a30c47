//! # Actor Subscription Module - STABLE DEVELOPER API
//!
//! ## Actor-to-Actor Event Subscriptions
//!
//! This module manages actor-to-actor subscriptions and event routing.
//! When actors subscribe to other actors' reactions, this system ensures
//! that reactions are delivered to all interested subscribers.
//!
//! ### API Stability Guarantee
//! The types and methods in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use rkyv::{Archive, Serialize, Deserialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::debug;

/// ## Subscription Information - STABLE API
///
/// ### Actor-to-Actor Event Subscriptions
/// Actors can subscribe to reactions from other actors.
/// When a subscribed reaction occurs, it triggers the subscriber's reaction handler.
///
/// ### API Stability
/// This struct and its fields are STABLE. Field names and types are guaranteed
/// to remain backward compatible within the same major version.
#[derive(Debug, <PERSON>lone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct Subscription {
    /// Subscriber actor ID
    pub subscriber: ActorId,
    /// Publisher actor ID (actor being subscribed to)
    pub publisher: ActorId,
    /// Reaction type name to subscribe to
    pub reaction_type: String,
    /// Subscription timestamp
    pub created_at: u64,
}

/// ## Subscribed Reaction - STABLE API
///
/// ### Reaction Delivered to Subscribers
/// When a subscribed reaction occurs, this is sent to all subscribers.
///
/// ### API Stability
/// This struct and its fields are STABLE. Field names and types are guaranteed
/// to remain backward compatible within the same major version.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct SubscribedReaction {
    /// Original reaction bytes
    pub reaction_bytes: Vec<u8>,
    /// Publisher actor ID
    pub publisher: ActorId,
    /// Reaction type name
    pub reaction_type: String,
    /// Delivery timestamp
    pub timestamp: u64,
}

/// ## Subscription Manager
///
/// ### Complete Subscription Management
/// Manages actor-to-actor subscriptions and event routing.
/// No simplified or stub subscription handling.
pub struct SubscriptionManager {
    /// Active subscriptions: publisher -> list of subscribers
    subscriptions: Arc<RwLock<HashMap<ActorId, Vec<Subscription>>>>,
    /// Reverse lookup: subscriber -> list of publishers
    reverse_subscriptions: Arc<RwLock<HashMap<ActorId, Vec<ActorId>>>>,
}

impl SubscriptionManager {
    /// Create new subscription manager
    pub fn new() -> Self {
        Self {
            subscriptions: Arc::new(RwLock::new(HashMap::new())),
            reverse_subscriptions: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Subscribe actor to another actor's reactions
    pub async fn subscribe(&self, subscription: Subscription) -> TamtilResult<()> {
        // Add to forward lookup
        {
            let mut subs = self.subscriptions.write().await;
            subs.entry(subscription.publisher.clone())
                .or_insert_with(Vec::new)
                .push(subscription.clone());
        }

        // Add to reverse lookup
        {
            let mut reverse = self.reverse_subscriptions.write().await;
            reverse.entry(subscription.subscriber.clone())
                .or_insert_with(Vec::new)
                .push(subscription.publisher.clone());
        }

        debug!("✅ Subscription created: {} -> {}",
               subscription.subscriber, subscription.publisher);

        Ok(())
    }

    /// Unsubscribe actor from another actor's reactions
    pub async fn unsubscribe(&self, subscriber: &ActorId, publisher: &ActorId) -> TamtilResult<()> {
        // Remove from forward lookup
        {
            let mut subs = self.subscriptions.write().await;
            if let Some(sub_list) = subs.get_mut(publisher) {
                sub_list.retain(|s| &s.subscriber != subscriber);
                if sub_list.is_empty() {
                    subs.remove(publisher);
                }
            }
        }

        // Remove from reverse lookup
        {
            let mut reverse = self.reverse_subscriptions.write().await;
            if let Some(pub_list) = reverse.get_mut(subscriber) {
                pub_list.retain(|p| p != publisher);
                if pub_list.is_empty() {
                    reverse.remove(subscriber);
                }
            }
        }

        debug!("❌ Subscription removed: {} -> {}", subscriber, publisher);

        Ok(())
    }

    /// Get all subscribers for a publisher
    pub async fn get_subscribers(&self, publisher: &ActorId) -> Vec<Subscription> {
        let subs = self.subscriptions.read().await;
        subs.get(publisher).cloned().unwrap_or_default()
    }

    /// Get all publishers for a subscriber
    pub async fn get_publishers(&self, subscriber: &ActorId) -> Vec<ActorId> {
        let reverse = self.reverse_subscriptions.read().await;
        reverse.get(subscriber).cloned().unwrap_or_default()
    }

    /// Create a subscribed reaction for delivery
    pub fn create_subscribed_reaction(
        &self,
        reaction_bytes: Vec<u8>,
        publisher: ActorId,
        reaction_type: String,
    ) -> SubscribedReaction {
        SubscribedReaction {
            reaction_bytes,
            publisher,
            reaction_type,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }
}

impl Default for SubscriptionManager {
    fn default() -> Self {
        Self::new()
    }
}
