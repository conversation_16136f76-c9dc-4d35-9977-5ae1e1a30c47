//! # Actor Module
//!
//! ## Actor System Implementation
//!
//! This module implements the core actor system for TAMTIL, following
//! <PERSON>'s actor pattern with action->reaction and remember->recall.
//! It provides complete actor lifecycle management, memory operations,
//! and subscription handling.
//!
//! ### Sub-modules
//! - `traits`: Core interfaces (Action, Reaction, Actor traits)
//! - `memory`: Actor memory management with event sourcing
//! - `subscription`: Actor-to-actor subscription system
//! - `core`: Generic actor implementation and handles
//!
//! ### Why Actor Model?
//! The actor model provides natural concurrency, fault isolation, and
//! location transparency. Combined with TAMTIL's consensus protocol,
//! it enables distributed computation with strong consistency guarantees.

pub mod traits;
pub mod memory;
pub mod subscription;
pub mod core;

// Re-export all public types and functions
pub use traits::*;
pub use memory::*;
pub use subscription::*;
pub use core::*;
