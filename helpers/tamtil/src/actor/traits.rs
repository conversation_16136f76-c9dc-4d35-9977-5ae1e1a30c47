//! # Actor Traits Module - STABLE DEVELOPER API
//!
//! ## Core Actor System Interfaces
//!
//! This module defines the STABLE interfaces (contracts) for actor behavior,
//! actions, and reactions. These traits provide stable contracts for
//! user-defined components and enable polymorphic actor behavior.
//!
//! ### API Stability Guarantee
//! The traits in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::{Subscription, SubscriptionManager, SubscribedReaction};
use async_trait::async_trait;

/// ## Action Trait (Alice Ryhl's Actor Pattern) - STABLE API
///
/// ### Complete Action Interface with Subscription Support
/// Actions contain business logic and produce reactions when executed.
/// Actions can also subscribe to other actors' reactions.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    ///
    /// ### Parameters
    /// - `memories`: Read-only access to actor's persistent state
    /// - `subscriptions`: Manager for creating and managing subscriptions
    ///
    /// ### Returns
    /// A reaction that will be applied to update actor state
    async fn act(&self, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor that will execute this action
    ///
    /// ### Returns
    /// Ok(()) if action is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }

    /// Get subscriptions this action wants to create
    ///
    /// ### Returns
    /// Vector of subscriptions to establish when this action executes
    fn get_subscriptions(&self) -> Vec<Subscription> {
        Vec::new() // Default: no subscriptions
    }

    /// Serialize action to bytes for network transmission and storage
    ///
    /// ### Returns
    /// Serialized bytes using rkyv zero-copy serialization
    fn to_bytes(&self) -> TamtilResult<Vec<u8>>;

    /// Deserialize action from bytes
    ///
    /// ### Parameters
    /// - `bytes`: Serialized action bytes
    ///
    /// ### Returns
    /// Deserialized action instance
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> where Self: Sized;
}

/// ## Reaction Trait (Event Sourcing) - STABLE API
///
/// ### Complete Reaction Interface with Subscription Support
/// Reactions are the single source of truth for state changes.
/// Reactions can also react to other reactions when they arrive via subscription.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this reaction when it arrives via subscription
    /// This method is ONLY executed when the reaction comes from a subscription
    ///
    /// ### Parameters
    /// - `subscribed_reaction`: The reaction that was received via subscription
    /// - `memories`: Read-only access to actor's persistent state
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    async fn react(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no reaction to subscribed reactions
    }

    /// Get reaction type name for subscription matching
    ///
    /// ### Returns
    /// String identifier for this reaction type
    fn reaction_type(&self) -> String {
        std::any::type_name::<Self>().to_string()
    }

    /// Serialize reaction to bytes for network transmission and storage
    ///
    /// ### Returns
    /// Serialized bytes using rkyv zero-copy serialization
    fn to_bytes(&self) -> TamtilResult<Vec<u8>>;

    /// Deserialize reaction from bytes
    ///
    /// ### Parameters
    /// - `bytes`: Serialized reaction bytes
    ///
    /// ### Returns
    /// Deserialized reaction instance
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> where Self: Sized;
}

/// ## Actor Trait (Alice Ryhl's Pattern)
///
/// ### Complete Actor Interface with Subscription Support
/// Actors process actions and produce reactions.
/// Actors can also handle subscribed reactions automatically.
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Vec<u8>>;

    /// Handle subscribed reaction (automatically called when subscribed reaction arrives)
    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    fn id(&self) -> &ActorId;
}
