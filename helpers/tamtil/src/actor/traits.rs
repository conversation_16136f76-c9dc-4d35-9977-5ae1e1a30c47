//! # Actor Traits Module - STABLE DEVELOPER API
//!
//! ## Core Actor System Interfaces
//!
//! This module defines the STABLE interfaces (contracts) for actor behavior,
//! actions, and reactions. These traits provide stable contracts for
//! user-defined components and enable polymorphic actor behavior.
//!
//! ### API Stability Guarantee
//! The traits in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::SubscribedReaction;
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize};

/// ## Actors Interface - STABLE API
///
/// ### Unified Actor Management Interface
/// Provides intuitive access to all actor operations: communication, subscription,
/// and lifecycle management. This is the primary interface developers use to
/// interact with the actor system.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Actors: Send + Sync + 'static {
    /// Get an actor reference for communication
    ///
    /// ### Parameters
    /// - `id`: The actor ID to communicate with
    ///
    /// ### Returns
    /// ActorRef that can send actions and subscribe to reactions
    ///
    /// ### Example
    /// ```rust
    /// let response = actors.actor(&user_id).act_bytes(action_bytes).await?;
    /// ```
    fn actor(&self, id: &ActorId) -> Box<dyn ActorRef>;

    /// Create a new child actor (using serialized actor)
    ///
    /// ### Parameters
    /// - `actor_bytes`: Serialized actor instance to create
    ///
    /// ### Returns
    /// The actual hierarchical ActorId that was assigned
    ///
    /// ### Hierarchical ID Structure
    /// Child actors are automatically namespaced under the current actor:
    /// If current actor is "platform.com/users/manager" and actor ID is "worker_1",
    /// the resulting ID will be "platform.com/users/manager/worker_1"
    async fn create_actor(&self, actor_bytes: Vec<u8>) -> TamtilResult<ActorId>;

    /// Start a child actor (if it was stopped)
    ///
    /// ### Parameters
    /// - `actor_id`: ID of the child actor to start
    ///
    /// ### Returns
    /// Ok(()) if actor was started successfully
    ///
    /// ### Note
    /// Only works on child actors of the current actor
    async fn start(&self, actor_id: &ActorId) -> TamtilResult<()>;

    /// Stop a child actor
    ///
    /// ### Parameters
    /// - `actor_id`: ID of the child actor to stop
    ///
    /// ### Returns
    /// Ok(()) if actor was stopped successfully
    ///
    /// ### Note
    /// Only works on child actors of the current actor.
    /// Stopping an actor also stops all its descendants.
    async fn stop(&self, actor_id: &ActorId) -> TamtilResult<()>;
}

/// Extension trait for convenient actor creation with type safety
#[async_trait]
pub trait ActorsExt: Actors {
    /// Create a new child actor with type safety
    ///
    /// ### Parameters
    /// - `actor`: The actor instance to create
    ///
    /// ### Returns
    /// The actual hierarchical ActorId that was assigned
    async fn create<A: Actor>(&self, actor: A) -> TamtilResult<ActorId> {
        // TODO: Serialize actor using rkyv
        // let actor_bytes = rkyv::to_bytes(&actor)?;
        // self.create_actor(actor_bytes).await

        // Placeholder implementation
        self.create_actor(b"placeholder_actor".to_vec()).await
    }
}

// Blanket implementation for all Actors
impl<T: Actors> ActorsExt for T {}

/// ## Actor Reference - STABLE API
///
/// ### Individual Actor Communication Interface
/// Provides methods to communicate with a specific actor through actions
/// and subscriptions.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait ActorRef: Send + Sync + 'static {
    /// Send serialized action bytes to this actor
    ///
    /// ### Parameters
    /// - `action_bytes`: Serialized action to send
    ///
    /// ### Returns
    /// Serialized reaction bytes produced by the actor
    ///
    /// ### Example
    /// ```rust
    /// let action_bytes = rkyv::to_bytes(&action)?;
    /// let reaction_bytes = actors.actor(&user_id).act_bytes(action_bytes).await?;
    /// let reaction = rkyv::from_bytes::<MyReaction>(&reaction_bytes)?;
    /// ```
    async fn act_bytes(&self, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>>;

    /// Subscribe to reactions from this actor
    ///
    /// ### Parameters
    /// - `reaction_type`: Type name of reactions to subscribe to
    ///
    /// ### Returns
    /// Ok(()) if subscription was created successfully
    ///
    /// ### Example
    /// ```rust
    /// actors.actor(&user_id).subscribe("UserUpdated").await?;
    /// ```
    async fn subscribe(&self, reaction_type: String) -> TamtilResult<()>;
}

/// Extension trait for convenient actor communication with type safety
#[async_trait]
pub trait ActorRefExt: ActorRef {
    /// Send a typed action to this actor
    ///
    /// ### Parameters
    /// - `action`: The action to send
    ///
    /// ### Returns
    /// The reaction produced by the actor
    ///
    /// ### Example
    /// ```rust
    /// let reaction = actors.actor(&user_id).act(UpdateEmailAction { new_email }).await?;
    /// ```
    async fn act<A: Action>(&self, action: A) -> TamtilResult<A::Reaction> {
        // TODO: Implement proper serialization/deserialization
        // let action_bytes = rkyv::to_bytes(&action)?;
        // let reaction_bytes = self.act_bytes(action_bytes).await?;
        // let reaction = rkyv::from_bytes::<A::Reaction>(&reaction_bytes)?;
        // Ok(reaction)

        // Placeholder implementation
        Err(crate::common_types::TamtilError::ActorNotFound {
            actor_id: "placeholder".to_string(),
        })
    }
}

// Blanket implementation for all ActorRef
impl<T: ActorRef> ActorRefExt for T {}

/// ## Action Trait (Alice Ryhl's Actor Pattern) - STABLE API
///
/// ### Core Action Interface
/// Actions contain business logic and produce reactions when executed.
/// Actors can subscribe to other actors during action execution.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    ///
    /// ### Parameters
    /// - `actors`: Unified interface for actor communication and management
    /// - `memories`: Read-only access to actor's persistent state
    ///
    /// ### Returns
    /// A reaction that will be applied to update actor state
    ///
    /// ### Example
    /// ```rust
    /// async fn act<A: Actors>(&self, actors: &A, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
    ///     // Call another actor
    ///     let action_bytes = rkyv::to_bytes(&GetUserAction { id: self.user_id })?;
    ///     let response_bytes = actors.actor(&user_id).act_bytes(action_bytes).await?;
    ///
    ///     // Subscribe to events
    ///     actors.actor(&notification_service).subscribe("UserUpdated".to_string()).await?;
    ///
    ///     // Create child actors
    ///     let worker = GenericActor::<WorkerAction>::new(ActorId::new("worker_1"));
    ///     let worker_id = actors.create(worker).await?;
    ///
    ///     Ok(MyReaction { data: response.data })
    /// }
    /// ```
    async fn act<A: Actors>(&self, actors: &A, memories: &ActorMemories) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor that will execute this action
    ///
    /// ### Returns
    /// Ok(()) if action is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Reaction Trait (Event Sourcing) - STABLE API
///
/// ### Core Reaction Interface
/// Reactions are the single source of truth for state changes.
/// They define what memory operations to apply when the reaction occurs.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;
}

/// ## RemoteReaction Trait - STABLE API
///
/// ### Remote Reaction Handling Interface
/// When a reaction arrives from a remote actor subscription, this trait
/// defines how to validate, remember, and react to it.
///
/// ### Usage Pattern
/// ```rust
/// // Import reaction type from remote actor
/// use remote_actor::SomeReaction;
///
/// // Implement RemoteReaction for handling remote reactions
/// impl RemoteReaction for SomeReaction {
///     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()> {
///         // Validate the remote reaction
///     }
///
///     fn remember(&self) -> Vec<MemoryOperation> {
///         // Define state changes for this remote reaction
///     }
///
///     async fn react(&self, memories: &ActorMemories) -> TamtilResult<()> {
///         // React to the remote reaction
///     }
/// }
/// ```
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate the remote reaction before processing
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor receiving this remote reaction
    ///
    /// ### Returns
    /// Ok(()) if remote reaction is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all remote reactions
    }

    /// Return memory operations to apply when this remote reaction arrives
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this remote reaction after it has been applied to memory
    ///
    /// ### Parameters
    /// - `actors`: Unified interface for actor communication and management
    /// - `memories`: Read-only access to actor's persistent state (after remember operations)
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    ///
    /// ### Example
    /// ```rust
    /// async fn react<A: Actors>(&self, actors: &A, memories: &ActorMemories) -> TamtilResult<()> {
    ///     // Can call other actors in response to remote reactions
    ///     let action_bytes = rkyv::to_bytes(&LogEventAction { event: "user_updated" })?;
    ///     actors.actor(&logger_id).act_bytes(action_bytes).await?;
    ///
    ///     // Can create new actors if needed
    ///     if self.requires_processing {
    ///         let processor = GenericActor::<ProcessorAction>::new(ActorId::new("processor"));
    ///         actors.create(processor).await?;
    ///     }
    ///
    ///     Ok(())
    /// }
    /// ```
    async fn react<A: Actors>(&self, _actors: &A, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no additional reaction
    }
}

/// ## Actor Trait (Alice Ryhl's Pattern) - STABLE API
///
/// ### Core Actor Interface
/// Actors process actions and produce reactions.
/// This trait is primarily implemented by GenericActor for most use cases.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    ///
    /// ### Parameters
    /// - `action_bytes`: Serialized action to process
    /// - `memories`: Actor's persistent state
    /// - `actors`: Unified interface for actor communication and management
    ///
    /// ### Returns
    /// Serialized reaction bytes
    async fn process<A: Actors>(&self, action_bytes: Vec<u8>, memories: &ActorMemories, actors: &A) -> TamtilResult<Vec<u8>>;

    /// Handle remote reaction from subscription
    ///
    /// ### Parameters
    /// - `subscribed_reaction`: Remote reaction that arrived via subscription
    /// - `memories`: Actor's persistent state
    /// - `actors`: Unified interface for actor communication and management
    ///
    /// ### Returns
    /// Ok(()) if handling succeeds
    async fn handle_subscribed_reaction<A: Actors>(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories, _actors: &A) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    ///
    /// ### Returns
    /// Reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}
