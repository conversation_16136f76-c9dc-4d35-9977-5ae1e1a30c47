//! # Actor Traits Module - STABLE DEVELOPER API
//!
//! ## Core Actor System Interfaces
//!
//! This module defines the STABLE interfaces (contracts) for actor behavior,
//! actions, and reactions. These traits provide stable contracts for
//! user-defined components and enable polymorphic actor behavior.
//!
//! ### API Stability Guarantee
//! The traits in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::{SubscriptionManager, SubscribedReaction};
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize};

/// ## Actor Factory - STABLE API
///
/// ### Actor Creation Interface
/// Allows actors to create new child actors during action execution.
/// Child actors are automatically stopped when parent actor stops.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait ActorFactory: Send + Sync + 'static {
    /// Create a new child actor
    ///
    /// ### Parameters
    /// - `child_id`: ID for the new child actor (will be made hierarchical)
    /// - `actor`: The actor instance to create
    ///
    /// ### Returns
    /// The actual hierarchical ActorId that was assigned
    ///
    /// ### Hierarchical ID Structure
    /// If parent is "platform.com/users/manager" and child_id is "worker_1",
    /// the resulting ID will be "platform.com/users/manager/worker_1"
    async fn create_actor<A: Actor>(&self, child_id: impl Into<String> + Send, actor: A) -> TamtilResult<ActorId>;

    /// Stop a child actor
    ///
    /// ### Parameters
    /// - `child_id`: ID of the child actor to stop
    ///
    /// ### Returns
    /// Ok(()) if actor was stopped successfully
    async fn stop_actor(&self, child_id: &ActorId) -> TamtilResult<()>;

    /// Get the current actor's ID (for building hierarchical IDs)
    fn current_actor_id(&self) -> &ActorId;
}

/// ## Action Trait (Alice Ryhl's Actor Pattern) - STABLE API
///
/// ### Core Action Interface
/// Actions contain business logic and produce reactions when executed.
/// Actors can subscribe to other actors during action execution.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    ///
    /// ### Parameters
    /// - `memories`: Read-only access to actor's persistent state
    /// - `subscriptions`: Manager for creating and managing subscriptions
    /// - `factory`: Factory for creating and managing child actors
    ///
    /// ### Returns
    /// A reaction that will be applied to update actor state
    async fn act(&self, memories: &ActorMemories, subscriptions: &SubscriptionManager, factory: &dyn ActorFactory) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor that will execute this action
    ///
    /// ### Returns
    /// Ok(()) if action is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Reaction Trait (Event Sourcing) - STABLE API
///
/// ### Core Reaction Interface
/// Reactions are the single source of truth for state changes.
/// They define what memory operations to apply when the reaction occurs.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;
}

/// ## RemoteReaction Trait - STABLE API
///
/// ### Remote Reaction Handling Interface
/// When a reaction arrives from a remote actor subscription, this trait
/// defines how to validate, remember, and react to it.
///
/// ### Usage Pattern
/// ```rust
/// // Import reaction type from remote actor
/// use remote_actor::SomeReaction;
///
/// // Implement RemoteReaction for handling remote reactions
/// impl RemoteReaction for SomeReaction {
///     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()> {
///         // Validate the remote reaction
///     }
///
///     fn remember(&self) -> Vec<MemoryOperation> {
///         // Define state changes for this remote reaction
///     }
///
///     async fn react(&self, memories: &ActorMemories) -> TamtilResult<()> {
///         // React to the remote reaction
///     }
/// }
/// ```
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate the remote reaction before processing
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor receiving this remote reaction
    ///
    /// ### Returns
    /// Ok(()) if remote reaction is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all remote reactions
    }

    /// Return memory operations to apply when this remote reaction arrives
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this remote reaction after it has been applied to memory
    ///
    /// ### Parameters
    /// - `memories`: Read-only access to actor's persistent state (after remember operations)
    /// - `subscriptions`: Manager for creating and managing subscriptions
    /// - `factory`: Factory for creating and managing child actors
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    async fn react(&self, _memories: &ActorMemories, _subscriptions: &SubscriptionManager, _factory: &dyn ActorFactory) -> TamtilResult<()> {
        Ok(()) // Default: no additional reaction
    }
}

/// ## Actor Trait (Alice Ryhl's Pattern) - STABLE API
///
/// ### Core Actor Interface
/// Actors process actions and produce reactions.
/// This trait is primarily implemented by GenericActor for most use cases.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    ///
    /// ### Parameters
    /// - `action_bytes`: Serialized action to process
    /// - `memories`: Actor's persistent state
    /// - `subscriptions`: Subscription manager for this actor
    /// - `factory`: Factory for creating and managing child actors
    ///
    /// ### Returns
    /// Serialized reaction bytes
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager, factory: &dyn ActorFactory) -> TamtilResult<Vec<u8>>;

    /// Handle remote reaction from subscription
    ///
    /// ### Parameters
    /// - `subscribed_reaction`: Remote reaction that arrived via subscription
    /// - `memories`: Actor's persistent state
    /// - `subscriptions`: Subscription manager for this actor
    /// - `factory`: Factory for creating and managing child actors
    ///
    /// ### Returns
    /// Ok(()) if handling succeeds
    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories, _subscriptions: &SubscriptionManager, _factory: &dyn ActorFactory) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    ///
    /// ### Returns
    /// Reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}
