//! # Actor Traits Module
//!
//! ## Core Actor System Interfaces
//!
//! This module defines the core interfaces (contracts) for actor behavior,
//! actions, and reactions. These traits provide stable contracts for
//! user-defined components and enable polymorphic actor behavior.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::{Subscription, SubscriptionManager, SubscribedReaction};
use async_trait::async_trait;

/// ## Action Trait (<PERSON> Ryhl's Actor Pattern)
///
/// ### Complete Action Interface with Subscription Support
/// Actions contain business logic and produce reactions when executed.
/// Actions can also subscribe to other actors' reactions.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    async fn act(&self, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }

    /// Get subscriptions this action wants to create
    fn get_subscriptions(&self) -> Vec<Subscription> {
        Vec::new() // Default: no subscriptions
    }

    /// Serialize action to bytes (production implementation)
    fn to_bytes(&self) -> TamtilResult<Vec<u8>>;

    /// Deserialize action from bytes (production implementation)
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> where Self: Sized;
}

/// ## Reaction Trait (Event Sourcing)
///
/// ### Complete Reaction Interface with Subscription Support
/// Reactions are the single source of truth for state changes.
/// Reactions can also react to other reactions when they arrive via subscription.
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this reaction when it arrives via subscription
    /// This method is ONLY executed when the reaction comes from a subscription
    async fn react(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no reaction to subscribed reactions
    }

    /// Get reaction type name for subscription matching
    fn reaction_type(&self) -> String {
        std::any::type_name::<Self>().to_string()
    }

    /// Serialize reaction to bytes (production implementation)
    fn to_bytes(&self) -> TamtilResult<Vec<u8>>;

    /// Deserialize reaction from bytes (production implementation)
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> where Self: Sized;
}

/// ## Actor Trait (Alice Ryhl's Pattern)
///
/// ### Complete Actor Interface with Subscription Support
/// Actors process actions and produce reactions.
/// Actors can also handle subscribed reactions automatically.
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Vec<u8>>;

    /// Handle subscribed reaction (automatically called when subscribed reaction arrives)
    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    fn id(&self) -> &ActorId;
}
