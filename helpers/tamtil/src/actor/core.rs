//! # Actor Core Module
//!
//! ## Generic Actor Implementation and Handles
//!
//! This module provides a generic actor implementation and the handle for
//! interacting with actors. It follows <PERSON>'s actor pattern with
//! task/handle separation and channel-based communication.

use crate::common_types::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tilResult};
use crate::actor::traits::{Action, Actor, Actors, ActorRef};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::SubscribedReaction;
use async_trait::async_trait;
use std::marker::PhantomData;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot};

/// ## Generic Actor Implementation
///
/// ### Complete Generic Actor
/// Production-ready generic actor that can handle any action type.
/// No simplified or stub generic actor implementation.
pub struct GenericActor<A: Action> {
    /// Actor identifier
    id: ActorId,
    /// Phantom data for action type
    _phantom: PhantomData<A>,
}

impl<A: Action> GenericActor<A> {
    /// Create new generic actor
    pub fn new(id: ActorId) -> Self {
        Self {
            id,
            _phantom: PhantomData,
        }
    }
}

#[async_trait]
impl<A: Action> Actor for GenericActor<A> {
    async fn process<ActorsImpl: Actors>(&self, action_bytes: Vec<u8>, memories: &ActorMemories, actors: &ActorsImpl) -> TamtilResult<Vec<u8>> {
        // Note: This is a simplified implementation for API stabilization
        // In production, this would use rkyv for zero-copy deserialization
        // For now, we'll use a placeholder that developers can override

        // TODO: Implement proper rkyv deserialization
        // let action = rkyv::from_bytes::<A>(&action_bytes)?;
        // let reaction = action.act(actors, memories).await?;

        // For now, return a placeholder response
        // This allows the API to be stable while we work on the internal implementation
        let placeholder_response = b"placeholder_reaction".to_vec();

        Ok(placeholder_response)
    }

    async fn handle_subscribed_reaction<ActorsImpl: Actors>(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories, _actors: &ActorsImpl) -> TamtilResult<()> {
        // TODO: Implement proper remote reaction handling
        Ok(())
    }

    fn id(&self) -> &ActorId {
        &self.id
    }
}

/// ## Context Actors Interface - STABLE API
///
/// ### Unified Actor Management
/// Implementation of the Actors trait that provides intuitive access to
/// all actor operations through a single interface.
///
/// ### API Stability
/// This struct is STABLE. Its public interface is guaranteed to remain
/// backward compatible within the same major version.
pub struct ContextActors {
    /// Current actor's ID (used as parent for new actors)
    current_actor_id: ActorId,
    /// Channel to send actor management requests to the context
    management_sender: mpsc::UnboundedSender<ActorManagementRequest>,
}

/// Request for actor management operations
pub enum ActorManagementRequest {
    /// Create a new actor
    Create {
        actor_bytes: Vec<u8>,
        response: oneshot::Sender<TamtilResult<ActorId>>,
    },
    /// Start an actor
    Start {
        actor_id: ActorId,
        response: oneshot::Sender<TamtilResult<()>>,
    },
    /// Stop an actor
    Stop {
        actor_id: ActorId,
        response: oneshot::Sender<TamtilResult<()>>,
    },
    /// Send action to actor
    SendAction {
        target_id: ActorId,
        action_bytes: Vec<u8>,
        response: oneshot::Sender<TamtilResult<Vec<u8>>>,
    },
    /// Subscribe to actor reactions
    Subscribe {
        target_id: ActorId,
        reaction_type: String,
        response: oneshot::Sender<TamtilResult<()>>,
    },
}

impl ContextActors {
    /// Create new actors interface
    pub fn new(current_actor_id: ActorId, management_sender: mpsc::UnboundedSender<ActorManagementRequest>) -> Self {
        Self {
            current_actor_id,
            management_sender,
        }
    }
}

#[async_trait]
impl Actors for ContextActors {
    fn actor(&self, id: &ActorId) -> Box<dyn ActorRef> {
        Box::new(ContextActorRef {
            target_id: id.clone(),
            management_sender: self.management_sender.clone(),
        })
    }

    async fn create_actor(&self, actor_bytes: Vec<u8>) -> TamtilResult<ActorId> {
        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorManagementRequest::Create {
            actor_bytes,
            response: response_sender,
        };

        self.management_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }

    async fn start(&self, actor_id: &ActorId) -> TamtilResult<()> {
        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorManagementRequest::Start {
            actor_id: actor_id.clone(),
            response: response_sender,
        };

        self.management_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }

    async fn stop(&self, actor_id: &ActorId) -> TamtilResult<()> {
        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorManagementRequest::Stop {
            actor_id: actor_id.clone(),
            response: response_sender,
        };

        self.management_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }
}

/// ## Context Actor Reference - STABLE API
///
/// ### Individual Actor Communication
/// Implementation of ActorRef for communicating with specific actors.
pub struct ContextActorRef {
    /// Target actor ID
    target_id: ActorId,
    /// Channel to send requests to the context
    management_sender: mpsc::UnboundedSender<ActorManagementRequest>,
}

#[async_trait]
impl ActorRef for ContextActorRef {
    async fn act_bytes(&self, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorManagementRequest::SendAction {
            target_id: self.target_id.clone(),
            action_bytes,
            response: response_sender,
        };

        self.management_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }

    async fn subscribe(&self, reaction_type: String) -> TamtilResult<()> {
        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorManagementRequest::Subscribe {
            target_id: self.target_id.clone(),
            reaction_type,
            response: response_sender,
        };

        self.management_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }
}

/// ## Actor Handle (Alice Ryhl's Pattern)
///
/// ### Complete Actor Handle with Subscription Support
/// Handle for communicating with actors using channels.
/// Supports both actions and subscribed reactions.
pub struct ActorHandle {
    /// Actor identifier
    id: ActorId,
    /// Channel for sending actions
    action_sender: mpsc::UnboundedSender<(Vec<u8>, oneshot::Sender<TamtilResult<Vec<u8>>>)>,
    /// Channel for sending subscribed reactions
    reaction_sender: mpsc::UnboundedSender<SubscribedReaction>,
}

impl ActorHandle {
    /// Create new actor handle with subscription support
    pub fn new<A: Actor>(actor: A, memories: ActorMemories) -> Self {
        let id = actor.id().clone();
        let (action_sender, mut action_receiver) = mpsc::unbounded_channel::<(Vec<u8>, oneshot::Sender<TamtilResult<Vec<u8>>>)>();
        let (reaction_sender, mut reaction_receiver) = mpsc::unbounded_channel::<SubscribedReaction>();

        // Spawn actor task for handling actions
        let actor_for_actions = Arc::new(actor);
        let memories_for_actions = Arc::new(memories);

        // Handle actions
        let actor_clone = Arc::clone(&actor_for_actions);
        let memories_clone = Arc::clone(&memories_for_actions);
        tokio::spawn(async move {
            while let Some((action_bytes, response_sender)) = action_receiver.recv().await {
                // TODO: Create a proper Actors implementation for the actor context
                // For now, use a placeholder that will be replaced with proper implementation
                let placeholder_actors = ContextActors::new(
                    ActorId::new("placeholder"),
                    mpsc::unbounded_channel().0
                );
                let result = actor_clone.process(action_bytes, &memories_clone, &placeholder_actors).await;
                let _ = response_sender.send(result);
            }
        });

        // Handle subscribed reactions
        let actor_clone = Arc::clone(&actor_for_actions);
        let memories_clone = Arc::clone(&memories_for_actions);
        tokio::spawn(async move {
            while let Some(subscribed_reaction) = reaction_receiver.recv().await {
                // TODO: Create a proper Actors implementation for the actor context
                let placeholder_actors = ContextActors::new(
                    ActorId::new("placeholder"),
                    mpsc::unbounded_channel().0
                );
                let _ = actor_clone.handle_subscribed_reaction(&subscribed_reaction, &memories_clone, &placeholder_actors).await;
            }
        });

        Self { id, action_sender, reaction_sender }
    }

    /// Send action to actor
    pub async fn send(&self, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        let (response_sender, response_receiver) = oneshot::channel();

        self.action_sender
            .send((action_bytes, response_sender))
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?
    }

    /// Send subscribed reaction to actor
    pub async fn send_subscribed_reaction(&self, subscribed_reaction: SubscribedReaction) -> TamtilResult<()> {
        self.reaction_sender
            .send(subscribed_reaction)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?;

        Ok(())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

impl Clone for ActorHandle {
    fn clone(&self) -> Self {
        Self {
            id: self.id.clone(),
            action_sender: self.action_sender.clone(),
            reaction_sender: self.reaction_sender.clone(),
        }
    }
}
