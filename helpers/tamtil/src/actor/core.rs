//! # Actor Core Module
//!
//! ## Generic Actor Implementation and Handles
//!
//! This module provides a generic actor implementation and the handle for
//! interacting with actors. It follows <PERSON>'s actor pattern with
//! task/handle separation and channel-based communication.

use crate::common_types::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>R<PERSON>ult};
use crate::actor::traits::{Action, Actor, ActorFactory};
use crate::actor::memory::ActorMemories;
use crate::actor::subscription::{SubscriptionManager, SubscribedReaction};
use async_trait::async_trait;
use std::marker::PhantomData;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot};

/// ## Generic Actor Implementation
///
/// ### Complete Generic Actor
/// Production-ready generic actor that can handle any action type.
/// No simplified or stub generic actor implementation.
pub struct GenericActor<A: Action> {
    /// Actor identifier
    id: ActorId,
    /// Phantom data for action type
    _phantom: PhantomData<A>,
}

impl<A: Action> GenericActor<A> {
    /// Create new generic actor
    pub fn new(id: ActorId) -> Self {
        Self {
            id,
            _phantom: PhantomData,
        }
    }
}

#[async_trait]
impl<A: Action> Actor for GenericActor<A> {
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager, factory: &dyn ActorFactory) -> TamtilResult<Vec<u8>> {
        // Note: This is a simplified implementation for API stabilization
        // In production, this would use rkyv for zero-copy deserialization
        // For now, we'll use a placeholder that developers can override

        // TODO: Implement proper rkyv deserialization
        // let action = rkyv::from_bytes::<A>(&action_bytes)?;
        // let reaction = action.act(memories, subscriptions, factory).await?;

        // For now, return a placeholder response
        // This allows the API to be stable while we work on the internal implementation
        let placeholder_response = b"placeholder_reaction".to_vec();

        Ok(placeholder_response)
    }

    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories, _subscriptions: &SubscriptionManager, _factory: &dyn ActorFactory) -> TamtilResult<()> {
        // TODO: Implement proper remote reaction handling
        Ok(())
    }

    fn id(&self) -> &ActorId {
        &self.id
    }
}

/// ## Context Actor Factory - STABLE API
///
/// ### Actor Creation and Management
/// Implementation of ActorFactory that manages hierarchical actor creation
/// and automatic cleanup when parent actors stop.
///
/// ### API Stability
/// This struct is STABLE. Its public interface is guaranteed to remain
/// backward compatible within the same major version.
pub struct ContextActorFactory {
    /// Current actor's ID (used as parent for new actors)
    current_actor_id: ActorId,
    /// Channel to send actor creation requests to the context
    creation_sender: mpsc::UnboundedSender<ActorCreationRequest>,
}

/// Request to create a new actor
pub struct ActorCreationRequest {
    /// The child actor ID to create
    pub child_id: String,
    /// The actor instance (boxed for type erasure)
    pub actor: Box<dyn Actor>,
    /// Response channel for the actual assigned ID
    pub response: oneshot::Sender<TamtilResult<ActorId>>,
}

/// Request to stop an actor
pub struct ActorStopRequest {
    /// The actor ID to stop
    pub actor_id: ActorId,
    /// Response channel for confirmation
    pub response: oneshot::Sender<TamtilResult<()>>,
}

impl ContextActorFactory {
    /// Create new actor factory
    pub fn new(current_actor_id: ActorId, creation_sender: mpsc::UnboundedSender<ActorCreationRequest>) -> Self {
        Self {
            current_actor_id,
            creation_sender,
        }
    }
}

#[async_trait]
impl ActorFactory for ContextActorFactory {
    async fn create_actor<A: Actor>(&self, child_id: impl Into<String> + Send, actor: A) -> TamtilResult<ActorId> {
        let child_name = child_id.into();
        let hierarchical_id = ActorId::child(&self.current_actor_id, &child_name);

        let (response_sender, response_receiver) = oneshot::channel();

        let request = ActorCreationRequest {
            child_id: child_name,
            actor: Box::new(actor),
            response: response_sender,
        };

        self.creation_sender
            .send(request)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: "context".to_string(),
            })?
    }

    async fn stop_actor(&self, child_id: &ActorId) -> TamtilResult<()> {
        // TODO: Implement actor stopping
        // This would send a stop request to the context
        Ok(())
    }

    fn current_actor_id(&self) -> &ActorId {
        &self.current_actor_id
    }
}

/// ## Actor Handle (Alice Ryhl's Pattern)
///
/// ### Complete Actor Handle with Subscription Support
/// Handle for communicating with actors using channels.
/// Supports both actions and subscribed reactions.
pub struct ActorHandle {
    /// Actor identifier
    id: ActorId,
    /// Channel for sending actions
    action_sender: mpsc::UnboundedSender<(Vec<u8>, oneshot::Sender<TamtilResult<Vec<u8>>>)>,
    /// Channel for sending subscribed reactions
    reaction_sender: mpsc::UnboundedSender<SubscribedReaction>,
}

impl ActorHandle {
    /// Create new actor handle with subscription support
    pub fn new<A: Actor>(actor: A, memories: ActorMemories, subscriptions: SubscriptionManager) -> Self {
        let id = actor.id().clone();
        let (action_sender, mut action_receiver) = mpsc::unbounded_channel::<(Vec<u8>, oneshot::Sender<TamtilResult<Vec<u8>>>)>();
        let (reaction_sender, mut reaction_receiver) = mpsc::unbounded_channel::<SubscribedReaction>();

        // Spawn actor task for handling actions
        let actor_for_actions = Arc::new(actor);
        let memories_for_actions = Arc::new(memories);
        let subscriptions_for_actions = Arc::new(subscriptions);

        // Handle actions
        let actor_clone = Arc::clone(&actor_for_actions);
        let memories_clone = Arc::clone(&memories_for_actions);
        let subscriptions_clone = Arc::clone(&subscriptions_for_actions);
        tokio::spawn(async move {
            while let Some((action_bytes, response_sender)) = action_receiver.recv().await {
                let result = actor_clone.process(action_bytes, &memories_clone, &subscriptions_clone).await;
                let _ = response_sender.send(result);
            }
        });

        // Handle subscribed reactions
        let actor_clone = Arc::clone(&actor_for_actions);
        let memories_clone = Arc::clone(&memories_for_actions);
        tokio::spawn(async move {
            while let Some(subscribed_reaction) = reaction_receiver.recv().await {
                let _ = actor_clone.handle_subscribed_reaction(&subscribed_reaction, &memories_clone).await;
            }
        });

        Self { id, action_sender, reaction_sender }
    }

    /// Send action to actor
    pub async fn send(&self, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        let (response_sender, response_receiver) = oneshot::channel();

        self.action_sender
            .send((action_bytes, response_sender))
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?;

        response_receiver
            .await
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?
    }

    /// Send subscribed reaction to actor
    pub async fn send_subscribed_reaction(&self, subscribed_reaction: SubscribedReaction) -> TamtilResult<()> {
        self.reaction_sender
            .send(subscribed_reaction)
            .map_err(|_| TamtilError::ActorNotFound {
                actor_id: self.id.to_string(),
            })?;

        Ok(())
    }

    /// Get actor ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

impl Clone for ActorHandle {
    fn clone(&self) -> Self {
        Self {
            id: self.id.clone(),
            action_sender: self.action_sender.clone(),
            reaction_sender: self.reaction_sender.clone(),
        }
    }
}
