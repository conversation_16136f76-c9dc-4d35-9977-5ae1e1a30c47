//! # TAMTIL: A Production-Ready Distributed Actor System
//!
//! ## Abstract
//!
//! This paper presents TAMTIL (Typed Actor Model with Transactional Isolated Logs), 
//! a novel distributed actor system that combines <PERSON> Ryhl's actor pattern with 
//! zero-copy serialization and embedded consensus. TAMTIL addresses the fundamental 
//! challenges of building fault-tolerant distributed systems while maintaining 
//! developer productivity through a unified programming model.
//!
//! ## 1. Introduction
//!
//! ### 1.1 Problem Statement
//!
//! Modern distributed systems face three critical challenges:
//! 1. **Performance vs Consistency Trade-off**: Traditional systems sacrifice 
//!    performance for consistency or vice versa
//! 2. **Complexity Explosion**: Different programming models for local vs 
//!    distributed execution increase cognitive load
//! 3. **Fault Tolerance Overhead**: Byzantine fault tolerance typically requires 
//!    significant performance penalties
//!
//! ### 1.2 Contributions
//!
//! TAMTIL makes the following novel contributions:
//! 1. **Unified Programming Model**: Identical developer experience for CLI and 
//!    web applications through transparent deployment modes
//! 2. **Zero-Copy Distributed Computing**: Leverages rkyv serialization for 
//!    near-zero overhead network communication
//! 3. **Hierarchical Actor Management**: Automatic resource cleanup through 
//!    parent-child actor relationships
//! 4. **Embedded Consensus**: OmniPaxos integration provides Byzantine fault 
//!    tolerance without external dependencies
//!
//! ## 2. System Architecture
//!
//! ### 2.1 Multi-Dimensional Design
//!
//! TAMTIL employs a three-dimensional architecture that scales from single 
//! processes to distributed clusters:
//!
//! #### Dimension 1: Hierarchical Organization
//! ```
//! Platform (Kubernetes-like Cluster)
//! ├── Context (Pod-like Container)
//! │   ├── Actor (Business Logic Unit)
//! │   ├── Actor (Child Actor)
//! │   │   └── Actor (Grandchild Actor)
//! │   └── Actor (Business Logic Unit)
//! └── Context (Pod-like Container)
//!     └── Actor (Business Logic Unit)
//! ```
//!
//! #### Dimension 2: Deployment Transparency
//! - **Local Mode**: Single-machine execution with direct memory communication
//! - **Distributed Mode**: Multi-machine execution with consensus-based replication
//!
//! #### Dimension 3: Communication Patterns
//! - **Action-Reaction**: Synchronous request-response with state changes
//! - **Subscription**: Asynchronous event-driven communication
//! - **Hierarchical**: Parent-child actor creation and management
//!
//! ### 2.2 Core Abstractions
//!
//! #### 2.2.1 Action Trait (Business Logic Encapsulation)
//!
//! Actions represent business operations that can be executed by actors:
//! ```rust
//! #[async_trait]
//! pub trait Action: Send + Sync + 'static {
//!     type Reaction: Reaction;
//!
//!     async fn act(&self, actors: &dyn Actors, memories: &ActorMemories) -> TamtilResult<Self::Reaction>;
//!     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: Actions encapsulate business logic with access to two
//! fundamental capabilities: actor communication (actors) and state access (memories).
//! The unified `actors` interface provides intuitive access to all actor operations.
//!
//! #### 2.2.2 Reaction Trait (State Change Specification)
//!
//! Reactions define atomic state changes using event sourcing:
//! ```rust
//! pub trait Reaction: Send + Sync + 'static {
//!     fn remember(&self) -> Vec<MemoryOperation>;
//! }
//! ```
//!
//! **Design Rationale**: Pure functional state changes enable deterministic 
//! replay, distributed consensus, and time-travel debugging.
//!
//! #### 2.2.3 Actors Interface (Unified Actor Management)
//!
//! The unified actors interface provides intuitive access to all actor operations:
//! ```rust
//! #[async_trait]
//! pub trait Actors: Send + Sync + 'static {
//!     fn actor(&self, id: &ActorId) -> Box<dyn ActorRef>;
//!     async fn create<A: Actor>(&self, actor: A) -> TamtilResult<ActorId>;
//!     async fn start(&self, actor_id: &ActorId) -> TamtilResult<()>;
//!     async fn stop(&self, actor_id: &ActorId) -> TamtilResult<()>;
//! }
//!
//! #[async_trait]
//! pub trait ActorRef: Send + Sync + 'static {
//!     async fn act<A: Action>(&self, action: A) -> TamtilResult<A::Reaction>;
//!     async fn subscribe(&self, reaction_type: impl Into<String> + Send) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: The unified interface makes actor interaction feel like
//! local function calls (`actors.actor(id).act(action)`) while providing hierarchical
//! resource management and automatic cleanup when parent actors terminate.
//!
//! ## 3. Event Sourcing and Memory Management
//!
//! ### 3.1 Memory Operations (Atomic State Primitives)
//!
//! TAMTIL provides five fundamental memory operations that are applied atomically:
//! ```rust
//! pub enum MemoryOperation {
//!     Set { key: String, value: Vec<u8> },      // Key-value storage
//!     Delete { key: String },                   // Key removal
//!     Increment { key: String, amount: i64 },   // Atomic counters
//!     Append { key: String, value: Vec<u8> },   // List operations
//!     Remove { key: String, index: usize },     // List element removal
//! }
//! ```
//!
//! **Design Rationale**: These operations cover the majority of state change 
//! patterns while maintaining atomicity guarantees essential for distributed 
//! consensus.
//!
//! ### 3.2 Actor Memories (Read-Only State Access)
//!
//! Actors access state through a read-only interface that prevents direct mutation:
//! ```rust
//! impl ActorMemories {
//!     async fn recall(&self, key: &str) -> TamtilResult<Option<Vec<u8>>>;
//!     async fn get_counter(&self, key: &str) -> TamtilResult<i64>;
//!     async fn get_list(&self, key: &str) -> TamtilResult<Vec<Vec<u8>>>;
//! }
//! ```
//!
//! **Design Rationale**: Read-only access ensures that all state changes flow 
//! through the reaction system, maintaining event sourcing invariants and 
//! enabling distributed replication.
//!
//! ## 4. Zero-Copy Serialization with rkyv
//!
//! ### 4.1 Performance Characteristics
//!
//! TAMTIL leverages rkyv for serialization, providing:
//! - **Zero-copy deserialization**: Access archived data without copying
//! - **Type preservation**: Full Rust type safety without type erasure
//! - **Validation**: Built-in data integrity verification
//! - **Performance**: 10-100x faster than serde for read-heavy workloads
//!
//! ### 4.2 Developer Requirements
//!
//! All actions and reactions must derive rkyv traits:
//! ```rust
//! #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
//! #[rkyv(derive(Debug))]
//! pub struct MyAction {
//!     field: String,
//! }
//! ```
//!
//! **Design Rationale**: Compile-time serialization contract ensures type safety 
//! and eliminates runtime serialization errors in distributed environments.
//!
//! ## 5. Distributed Consensus Integration
//!
//! ### 5.1 OmniPaxos Embedding
//!
//! TAMTIL embeds OmniPaxos consensus for reaction ordering:
//! ```
//! Action → Actor → Reaction → Consensus → Apply to All Nodes → Notify Subscribers
//! ```
//!
//! **Design Rationale**: Embedded consensus eliminates external dependencies 
//! while providing mathematical guarantees for Byzantine fault tolerance.
//!
//! ### 5.2 Consensus Scope
//!
//! Consensus operates at the reaction level, ensuring:
//! - **Reaction Ordering**: Consistent application order across all nodes
//! - **State Replication**: Identical state on all cluster members
//! - **Byzantine Fault Tolerance**: Handles up to (n-1)/3 malicious nodes
//!
//! ## 6. Hierarchical Actor Management
//!
//! ### 6.1 Dynamic Actor Creation
//!
//! Actors can create child actors and communicate with any actor in the platform:
//! ```rust
//! async fn act(&self, actors: &dyn Actors, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
//!     // Call any actor in the platform
//!     let user_data = actors.actor(&user_id).act(GetUserAction { id: self.user_id }).await?;
//!
//!     // Subscribe to events from other actors
//!     actors.actor(&notification_service).subscribe("UserUpdated").await?;
//!
//!     // Create child actors
//!     let worker = GenericActor::<WorkerAction>::new(ActorId::new("worker_1"));
//!     let child_id = actors.create(worker).await?;
//!     // child_id becomes "parent_id/worker_1"
//!
//!     // Manage child actors
//!     actors.stop(&child_id).await?;
//!     actors.start(&child_id).await?;
//! }
//! ```
//!
//! ### 6.2 Automatic Resource Cleanup
//!
//! When a parent actor stops, all descendant actors are automatically terminated:
//! ```
//! "manager" stops → "manager/worker_1" stops → "manager/worker_1/helper" stops
//! ```
//!
//! **Design Rationale**: Hierarchical cleanup prevents resource leaks and 
//! simplifies resource management in complex actor hierarchies.
//!
//! ## 7. Subscription System (Event-Driven Architecture)
//!
//! ### 7.1 Cross-Actor Communication
//!
//! Actors subscribe to reactions from other actors for event-driven coordination:
//! ```
//! 1. Actor A subscribes to Actor B's reactions
//! 2. Actor B produces a reaction
//! 3. Reaction delivered to Actor A
//! 4. RemoteReaction::validate() → remember() → react()
//! ```
//!
//! ### 7.2 RemoteReaction Processing
//!
//! Remote reactions follow a three-phase protocol:
//! ```rust
//! #[async_trait]
//! pub trait RemoteReaction: Send + Sync + 'static {
//!     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()>;
//!     fn remember(&self) -> Vec<MemoryOperation>;
//!     async fn react(&self, actors: &dyn Actors, memories: &ActorMemories) -> TamtilResult<()>;
//! }
//! ```
//!
//! **Design Rationale**: Three-phase processing ensures security (validate),
//! consistency (remember), and extensibility (react with full actor capabilities).

mod common_types;
pub mod consensus;
pub mod actor;
pub mod http;
pub mod platform;

// Re-export all public APIs
pub use common_types::*;
pub use consensus::*;
pub use actor::*;
pub use http::*;
pub use platform::*;

