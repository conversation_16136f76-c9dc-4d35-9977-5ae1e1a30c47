//! # Platform Module
//!
//! ## Platform and Context Management
//!
//! This module implements the top-level platform and context management
//! for TAMTIL. It provides unified CLI/Web development experience with
//! transparent local vs distributed execution.
//!
//! ### Sub-modules
//! - `context`: Context implementation (pod-like containers)
//! - `definition`: Platform implementation and deployment modes
//!
//! ### Why Platform/Context Architecture?
//! The hierarchical Platform->Context->Actor architecture mirrors
//! Kubernetes patterns, providing familiar abstractions for distributed
//! system management while maintaining simplicity for local development.

pub mod context;
pub mod definition;

// Re-export all public types and functions
pub use context::*;
pub use definition::*;
