//! # Platform Definition Module - STABLE DEVELOPER API
//!
//! ## Platform Implementation and Deployment Modes
//!
//! This module implements the main Platform struct and deployment mode
//! management. It provides unified CLI/Web development experience with
//! transparent local vs distributed execution.
//!
//! ### API Stability Guarantee
//! The Platform struct and its public methods are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, NodeId, TamtilError, TamtilResult};
use crate::platform::context::Context;
use crate::actor::{Actor, ActorHandle, ActorMemories, SubscriptionManager};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// ## Deployment Mode - STABLE API
///
/// ### Transparent Local vs Distributed Execution
/// Developers use the same patterns regardless of deployment mode.
/// The platform automatically handles local vs distributed execution.
///
/// ### API Stability
/// This enum is STABLE. Variants are guaranteed to remain backward compatible
/// within the same major version.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>q, Eq)]
pub enum DeploymentMode {
    /// Local mode for CLI applications (single machine, no consensus)
    Local,
    /// Distributed mode for web applications (multi-machine, with consensus)
    Distributed,
}

/// ## Platform (Main Node) - STABLE API
///
/// ### Unified CLI/Web Development Experience
/// Platform provides the same actor patterns for both CLI and web applications.
/// Distribution is transparent to developers - same code works in both modes.
///
/// ### API Stability
/// This struct and its public methods are STABLE. Method signatures and behavior
/// are guaranteed to remain backward compatible within the same major version.
pub struct Platform {
    /// Platform identifier
    id: ActorId,
    /// Contexts in this platform
    contexts: Arc<RwLock<HashMap<ActorId, Context>>>,
    /// Deployment mode (local vs distributed)
    mode: DeploymentMode,
    /// Node ID for consensus (only used in distributed mode)
    node_id: NodeId,
    /// Peer nodes (only used in distributed mode)
    peers: Vec<NodeId>,
}

impl Platform {
    /// Create local platform for CLI applications - STABLE API
    ///
    /// ### Why Local Mode?
    /// CLI applications run on a single machine and don't need distributed consensus.
    /// This provides maximum performance for local development and CLI tools.
    ///
    /// ### Parameters
    /// - `id`: Platform identifier
    ///
    /// ### Returns
    /// Platform configured for local execution
    pub fn local(id: impl Into<String>) -> Self {
        Self {
            id: ActorId::new(id),
            contexts: Arc::new(RwLock::new(HashMap::new())),
            mode: DeploymentMode::Local,
            node_id: 1, // Single node
            peers: Vec::new(), // No peers in local mode
        }
    }

    /// Create distributed platform for web applications - STABLE API
    ///
    /// ### Why Distributed Mode?
    /// Web applications need fault tolerance and can span multiple machines.
    /// This enables consensus-based replication and network communication.
    ///
    /// ### Parameters
    /// - `id`: Platform identifier
    /// - `node_id`: This node's unique identifier
    /// - `peers`: List of peer node identifiers
    ///
    /// ### Returns
    /// Platform configured for distributed execution
    pub fn distributed(id: impl Into<String>, node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self {
            id: ActorId::new(id),
            contexts: Arc::new(RwLock::new(HashMap::new())),
            mode: DeploymentMode::Distributed,
            node_id,
            peers,
        }
    }

    /// Create new platform (legacy method - defaults to distributed)
    ///
    /// ### Backward Compatibility
    /// Maintains compatibility with existing code while encouraging
    /// developers to use explicit local() or distributed() methods.
    pub fn new(id: impl Into<String>, node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self::distributed(id, node_id, peers)
    }

    /// Create new context - STABLE API
    ///
    /// ### Transparent Mode Handling
    /// Context creation automatically adapts to deployment mode.
    /// Local mode: No consensus overhead, direct communication
    /// Distributed mode: Full consensus and network communication
    ///
    /// ### Parameters
    /// - `context_id`: Unique identifier for the new context
    ///
    /// ### Returns
    /// Ok(()) if context creation succeeds
    pub async fn create_context(&self, context_id: impl Into<String>) -> TamtilResult<()> {
        let context_id = ActorId::new(context_id);

        let context = match self.mode {
            DeploymentMode::Local => {
                // Local mode: Create context without consensus overhead
                Context::local(context_id.clone())
            }
            DeploymentMode::Distributed => {
                // Distributed mode: Create context with full consensus
                Context::new(context_id.clone(), self.node_id, self.peers.clone())
            }
        };

        let mut contexts = self.contexts.write().await;
        contexts.insert(context_id, context);

        Ok(())
    }

    /// Get context by ID
    pub async fn get_context(&self, context_id: &ActorId) -> TamtilResult<Context> {
        let contexts = self.contexts.read().await;
        contexts.get(context_id).cloned()
            .ok_or_else(|| TamtilError::ContextNotFound {
                context_id: context_id.to_string(),
            })
    }

    /// Add actor to context - STABLE API
    ///
    /// ### Parameters
    /// - `context_id`: ID of the context to add the actor to
    /// - `actor`: Actor instance to add
    ///
    /// ### Returns
    /// Ok(()) if actor addition succeeds
    pub async fn add_actor_to_context<A: Actor>(&self, context_id: &ActorId, actor: A) -> TamtilResult<()> {
        let context = self.get_context(context_id).await?;
        let memories = ActorMemories::new(actor.id().clone());
        let subscriptions = SubscriptionManager::new();
        let handle = ActorHandle::new(actor, memories, subscriptions);
        context.add_actor(handle).await;
        Ok(())
    }

    /// Send action to actor
    pub async fn send_action(&self, context_id: &ActorId, actor_id: &ActorId, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        let context = self.get_context(context_id).await?;
        context.send_action(actor_id, action_bytes).await
    }

    /// Get platform ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Get node ID
    pub fn node_id(&self) -> NodeId {
        self.node_id
    }

    /// Get peers
    pub fn peers(&self) -> &[NodeId] {
        &self.peers
    }

    /// Get deployment mode
    ///
    /// ### Why Expose Mode?
    /// Allows developers to conditionally enable features based on deployment mode.
    /// For example, HTTP servers only make sense in distributed mode.
    pub fn mode(&self) -> DeploymentMode {
        self.mode
    }

    /// Check if platform is running in local mode
    ///
    /// ### Why Local Check?
    /// Convenient helper for CLI-specific logic that should only run locally.
    pub fn is_local(&self) -> bool {
        self.mode == DeploymentMode::Local
    }

    /// Check if platform is running in distributed mode
    ///
    /// ### Why Distributed Check?
    /// Convenient helper for web-specific logic that requires distribution.
    pub fn is_distributed(&self) -> bool {
        self.mode == DeploymentMode::Distributed
    }
}
