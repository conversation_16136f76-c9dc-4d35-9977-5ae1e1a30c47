//! # Platform Definition Module
//!
//! ## Platform Implementation and Deployment Modes
//!
//! This module implements the main Platform struct and deployment mode
//! management. It provides unified CLI/Web development experience with
//! transparent local vs distributed execution.

use crate::common_types::{<PERSON><PERSON><PERSON>, <PERSON>de<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::platform::context::Context;
use crate::actor::{Actor, ActorHandle, ActorMemories, SubscriptionManager};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// ## Deployment Mode
///
/// ### Transparent Local vs Distributed Execution
/// Developers use the same patterns regardless of deployment mode.
/// The platform automatically handles local vs distributed execution.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DeploymentMode {
    /// Local mode for CLI applications (single machine, no consensus)
    Local,
    /// Distributed mode for web applications (multi-machine, with consensus)
    Distributed,
}

/// ## Platform (Main Node)
///
/// ### Unified CLI/Web Development Experience
/// Platform provides the same actor patterns for both CLI and web applications.
/// Distribution is transparent to developers - same code works in both modes.
pub struct Platform {
    /// Platform identifier
    id: ActorId,
    /// Contexts in this platform
    contexts: Arc<RwLock<HashMap<ActorId, Context>>>,
    /// Deployment mode (local vs distributed)
    mode: DeploymentMode,
    /// Node ID for consensus (only used in distributed mode)
    node_id: NodeId,
    /// Peer nodes (only used in distributed mode)
    peers: Vec<NodeId>,
}

impl Platform {
    /// Create local platform for CLI applications
    ///
    /// ### Why Local Mode?
    /// CLI applications run on a single machine and don't need distributed consensus.
    /// This provides maximum performance for local development and CLI tools.
    pub fn local(id: impl Into<String>) -> Self {
        Self {
            id: ActorId::new(id),
            contexts: Arc::new(RwLock::new(HashMap::new())),
            mode: DeploymentMode::Local,
            node_id: 1, // Single node
            peers: Vec::new(), // No peers in local mode
        }
    }

    /// Create distributed platform for web applications
    ///
    /// ### Why Distributed Mode?
    /// Web applications need fault tolerance and can span multiple machines.
    /// This enables consensus-based replication and network communication.
    pub fn distributed(id: impl Into<String>, node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self {
            id: ActorId::new(id),
            contexts: Arc::new(RwLock::new(HashMap::new())),
            mode: DeploymentMode::Distributed,
            node_id,
            peers,
        }
    }

    /// Create new platform (legacy method - defaults to distributed)
    ///
    /// ### Backward Compatibility
    /// Maintains compatibility with existing code while encouraging
    /// developers to use explicit local() or distributed() methods.
    pub fn new(id: impl Into<String>, node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self::distributed(id, node_id, peers)
    }

    /// Create new context
    ///
    /// ### Transparent Mode Handling
    /// Context creation automatically adapts to deployment mode.
    /// Local mode: No consensus overhead, direct communication
    /// Distributed mode: Full consensus and network communication
    pub async fn create_context(&self, context_id: impl Into<String>) -> TamtilResult<()> {
        let context_id = ActorId::new(context_id);

        let context = match self.mode {
            DeploymentMode::Local => {
                // Local mode: Create context without consensus overhead
                Context::local(context_id.clone())
            }
            DeploymentMode::Distributed => {
                // Distributed mode: Create context with full consensus
                Context::new(context_id.clone(), self.node_id, self.peers.clone())
            }
        };

        let mut contexts = self.contexts.write().await;
        contexts.insert(context_id, context);

        Ok(())
    }

    /// Get context by ID
    pub async fn get_context(&self, context_id: &ActorId) -> TamtilResult<Context> {
        let contexts = self.contexts.read().await;
        contexts.get(context_id).cloned()
            .ok_or_else(|| TamtilError::ContextNotFound {
                context_id: context_id.to_string(),
            })
    }

    /// Add actor to context
    pub async fn add_actor_to_context<A: Actor>(&self, context_id: &ActorId, actor: A) -> TamtilResult<()> {
        let context = self.get_context(context_id).await?;
        let memories = ActorMemories::new(actor.id().clone());
        let subscriptions = SubscriptionManager::new();
        let handle = ActorHandle::new(actor, memories, subscriptions);
        context.add_actor(handle).await;
        Ok(())
    }

    /// Send action to actor
    pub async fn send_action(&self, context_id: &ActorId, actor_id: &ActorId, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        let context = self.get_context(context_id).await?;
        context.send_action(actor_id, action_bytes).await
    }

    /// Get platform ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Get node ID
    pub fn node_id(&self) -> NodeId {
        self.node_id
    }

    /// Get peers
    pub fn peers(&self) -> &[NodeId] {
        &self.peers
    }

    /// Get deployment mode
    ///
    /// ### Why Expose Mode?
    /// Allows developers to conditionally enable features based on deployment mode.
    /// For example, HTTP servers only make sense in distributed mode.
    pub fn mode(&self) -> DeploymentMode {
        self.mode
    }

    /// Check if platform is running in local mode
    ///
    /// ### Why Local Check?
    /// Convenient helper for CLI-specific logic that should only run locally.
    pub fn is_local(&self) -> bool {
        self.mode == DeploymentMode::Local
    }

    /// Check if platform is running in distributed mode
    ///
    /// ### Why Distributed Check?
    /// Convenient helper for web-specific logic that requires distribution.
    pub fn is_distributed(&self) -> bool {
        self.mode == DeploymentMode::Distributed
    }
}
