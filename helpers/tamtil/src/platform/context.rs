//! # Platform Context Module
//!
//! ## Context Implementation (Pod-like Containers)
//!
//! This module implements the Context struct, which manages groups of actors
//! like Kubernetes pods. Contexts integrate with consensus, handle subscriptions,
//! and can host HTTP servers for web applications.

use crate::common_types::{ActorId, NodeId, <PERSON><PERSON><PERSON>rror, TamtilResult};
use crate::consensus::{TamtilConsensus, ConsensusMessage, TamtilEntry};
use crate::actor::{ActorHandle, SubscriptionManager, SubscribedReaction};
use crate::http::server::HttpServer;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::{RwLock, Mutex};
use tracing::{debug, error, info};

/// ## Context (Pod-like Container)
///
/// ### Complete Context Implementation with HTTP and Subscriptions
/// Contexts manage groups of actors like Kubernetes pods.
/// Includes HTTP server and subscription management.
pub struct Context {
    /// Context identifier
    id: ActorId,
    /// Actors in this context
    actors: Arc<RwLock<HashMap<ActorId, ActorHandle>>>,
    /// Consensus manager
    consensus: Arc<Mutex<TamtilConsensus>>,
    /// Subscription manager
    subscriptions: Arc<SubscriptionManager>,
    /// HTTP server (optional)
    http_server: Arc<RwLock<Option<HttpServer>>>,
}

impl Context {
    /// Create new distributed context with consensus support
    ///
    /// ### Why Distributed Context?
    /// For web applications that need fault tolerance and multi-machine deployment.
    /// Includes full consensus protocol and network communication capabilities.
    pub fn new(id: ActorId, node_id: NodeId, peers: Vec<NodeId>) -> Self {
        Self {
            id,
            actors: Arc::new(RwLock::new(HashMap::new())),
            consensus: Arc::new(Mutex::new(TamtilConsensus::new(node_id, peers))),
            subscriptions: Arc::new(SubscriptionManager::new()),
            http_server: Arc::new(RwLock::new(None)),
        }
    }

    /// Create new local context without consensus overhead
    ///
    /// ### Why Local Context?
    /// For CLI applications that run on a single machine and don't need consensus.
    /// Provides maximum performance by skipping distributed coordination.
    pub fn local(id: ActorId) -> Self {
        Self {
            id,
            actors: Arc::new(RwLock::new(HashMap::new())),
            // Local mode: Single node consensus (no network overhead)
            consensus: Arc::new(Mutex::new(TamtilConsensus::new(1, Vec::new()))),
            subscriptions: Arc::new(SubscriptionManager::new()),
            http_server: Arc::new(RwLock::new(None)),
        }
    }

    /// Add actor to context
    pub async fn add_actor(&self, handle: ActorHandle) {
        let mut actors = self.actors.write().await;
        actors.insert(handle.id().clone(), handle);
    }

    /// Get actor by ID
    pub async fn get_actor(&self, actor_id: &ActorId) -> Option<ActorHandle> {
        let actors = self.actors.read().await;
        actors.get(actor_id).cloned()
    }

    /// Send action to actor with consensus and subscription handling
    pub async fn send_action(&self, actor_id: &ActorId, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>> {
        // Get actor handle
        let handle = {
            let actors = self.actors.read().await;
            actors.get(actor_id).cloned()
                .ok_or_else(|| TamtilError::ActorNotFound {
                    actor_id: actor_id.to_string(),
                })?
        };

        // Send action to actor
        let reaction_bytes = handle.send(action_bytes).await?;

        // Propose reaction to consensus
        {
            let mut consensus = self.consensus.lock().await;
            consensus.propose_entry(reaction_bytes.clone(), actor_id.clone())?;
        }

        // Handle subscriptions - notify subscribers of this reaction
        self.handle_reaction_subscriptions(actor_id, &reaction_bytes).await?;

        Ok(reaction_bytes)
    }

    /// Handle reaction subscriptions - notify all subscribers
    async fn handle_reaction_subscriptions(&self, publisher: &ActorId, reaction_bytes: &[u8]) -> TamtilResult<()> {
        // Get all subscribers for this publisher
        let subscribers = self.subscriptions.get_subscribers(publisher).await;

        if subscribers.is_empty() {
            return Ok(());
        }

        debug!("📢 Notifying {} subscribers of reaction from {}", subscribers.len(), publisher);

        // Create subscribed reaction
        // Extract reaction type from the first 8 bytes which contain type information
        let reaction_type = if reaction_bytes.len() >= 8 {
            format!("reaction_{}", u64::from_le_bytes([
                reaction_bytes[0], reaction_bytes[1], reaction_bytes[2], reaction_bytes[3],
                reaction_bytes[4], reaction_bytes[5], reaction_bytes[6], reaction_bytes[7]
            ]))
        } else {
            "unknown_reaction".to_string()
        };

        let subscribed_reaction = SubscribedReaction {
            reaction_bytes: reaction_bytes.to_vec(),
            publisher: publisher.clone(),
            reaction_type,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        };

        // Send to all subscribers
        let actors = self.actors.read().await;
        for subscription in subscribers {
            if let Some(subscriber_handle) = actors.get(&subscription.subscriber) {
                if let Err(e) = subscriber_handle.send_subscribed_reaction(subscribed_reaction.clone()).await {
                    error!("❌ Failed to send subscribed reaction to {}: {}", subscription.subscriber, e);
                }
            }
        }

        Ok(())
    }

    /// Start HTTP server for this context
    pub async fn start_http_server(&self, address: SocketAddr) -> TamtilResult<()> {
        let context_arc = Arc::new(self.clone());
        let mut server = HttpServer::new(address, context_arc);

        // Add default routes for all actors
        {
            let actors = self.actors.read().await;
            for actor_id in actors.keys() {
                let path = format!("/{}", actor_id.id);
                server.add_route(&path, actor_id.clone())?;
            }
        }

        // Store server reference
        {
            let mut http_server = self.http_server.write().await;
            *http_server = Some(server);
        }

        // Start server (this will block)
        if let Some(_server) = self.http_server.read().await.as_ref() {
            // Note: In real implementation, we'd need to clone the server
            // For now, we'll just log that it would start
            info!("🌐 HTTP server would start on {}", address);
        }

        Ok(())
    }

    /// Subscribe actor to another actor's reactions
    pub async fn subscribe_actor(&self, subscriber: &ActorId, publisher: &ActorId, reaction_type: String) -> TamtilResult<()> {
        use crate::actor::subscription::Subscription;

        let subscription = Subscription {
            subscriber: subscriber.clone(),
            publisher: publisher.clone(),
            reaction_type,
            created_at: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        };

        self.subscriptions.subscribe(subscription).await?;

        info!("📡 Actor {} subscribed to {} reactions", subscriber, publisher);

        Ok(())
    }

    /// Unsubscribe actor from another actor's reactions
    pub async fn unsubscribe_actor(&self, subscriber: &ActorId, publisher: &ActorId) -> TamtilResult<()> {
        self.subscriptions.unsubscribe(subscriber, publisher).await?;

        info!("📡 Actor {} unsubscribed from {}", subscriber, publisher);

        Ok(())
    }

    /// Process consensus messages
    pub async fn handle_consensus_message(&self, from: NodeId, message: ConsensusMessage) -> TamtilResult<()> {
        let mut consensus = self.consensus.lock().await;
        consensus.handle_message(from, message)
    }

    /// Get outgoing consensus messages
    pub async fn take_consensus_messages(&self) -> Vec<(NodeId, ConsensusMessage)> {
        let mut consensus = self.consensus.lock().await;
        consensus.take_outgoing_messages()
    }

    /// Get decided entries
    pub async fn get_decided_entries(&self) -> TamtilResult<Vec<TamtilEntry>> {
        let consensus = self.consensus.lock().await;
        consensus.get_decided_entries()
    }

    /// Get context ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }
}

impl Clone for Context {
    fn clone(&self) -> Self {
        Self {
            id: self.id.clone(),
            actors: Arc::clone(&self.actors),
            consensus: Arc::clone(&self.consensus),
            subscriptions: Arc::clone(&self.subscriptions),
            http_server: Arc::clone(&self.http_server),
        }
    }
}
