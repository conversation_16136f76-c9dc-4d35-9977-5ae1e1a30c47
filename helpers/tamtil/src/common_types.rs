//! # Common Types Module - STABLE DEVELOPER API
//!
//! ## Core Shared Types for TAMTIL
//!
//! This module provides fundamental data types and error handling used across
//! the entire TAMTIL system. These types are pervasive and centralizing them
//! avoids circular dependencies and makes them easy to find.
//!
//! ### API Stability Guarantee
//! The types in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.
//!
//! ### Contents
//! - `NodeId`: Node identifier type for consensus
//! - `Ballot`: Ballot structure for consensus ordering
//! - `ActorId`: Actor identifier with URL-based addressing
//! - `TamtilError`: Centralized error type
//! - `TamtilResult<T>`: Centralized result type

use rkyv::{Archive, Serialize, Deserialize};
use std::fmt;

/// Node identifier type - simple u64 for maximum performance
pub type NodeId = u64;

/// ## Ballot Structure (Copied from OmniPaxos)
/// 
/// ### Mathematical Foundation: Lamport's Paxos
/// Ballots provide total ordering across distributed nodes. Higher ballot
/// numbers always take precedence, ensuring consensus safety properties.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, PartialOrd, Ord, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct Ballot {
    /// Ballot number - monotonically increasing
    pub n: u64,
    /// Proposer node ID for tie-breaking
    pub pid: NodeId,
}

impl Default for Ballot {
    fn default() -> Self {
        Self { n: 0, pid: 0 }
    }
}

/// ## Actor Identifier - STABLE API
///
/// ### URL-Based Addressing with Interning
/// Actors are addressed using URL-like strings that are interned for performance.
/// Format: platform.com/context_name/context_id/actor_name/actor_id
///
/// ### API Stability
/// This struct and its methods are STABLE. Field names and method signatures
/// are guaranteed to remain backward compatible within the same major version.
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId {
    /// Interned string representation
    pub id: String,
}

impl ActorId {
    /// Create new actor ID
    ///
    /// ### Parameters
    /// - `id`: String identifier for the actor
    ///
    /// ### Returns
    /// New ActorId instance
    pub fn new(id: impl Into<String>) -> Self {
        Self { id: id.into() }
    }
}

impl fmt::Display for ActorId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.id)
    }
}

/// ## Error Types - STABLE API
///
/// ### Complete Error Handling
/// Production-ready error types covering all failure modes without any
/// placeholder or stub error handling.
///
/// ### API Stability
/// This enum and its variants are STABLE. Error types and their fields
/// are guaranteed to remain backward compatible within the same major version.
#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },
    
    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },
    
    #[error("Consensus timeout: {operation}")]
    ConsensusTimeout { operation: String },
    
    #[error("Platform initialization failed: {reason}")]
    PlatformInitFailed { reason: String },
    
    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },
    
    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },
    
    #[error("Storage error: {message}")]
    Storage { message: String },
    
    #[error("Network error: {message}")]
    Network { message: String },
    
    #[error("Validation failed: {message}")]
    Validation { message: String },
}

pub type TamtilResult<T> = Result<T, TamtilError>;
