# TAMTIL Stable Developer API

## Overview

This document defines the **STABLE** developer-facing APIs for TAMTIL. These APIs follow semantic versioning and are guaranteed to remain backward compatible within the same major version.

## Core Principle: The "Actor Model Contract" Must Be Sacrosanct

The APIs developers use to define and interact with actors are the most critical. These have reached stability and will change extremely rarely, if ever, in ways that break user code.

## Phase 1: Stabilized Core Actor Model Primitives ✅

### Action Trait - STABLE API

```rust
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    async fn act(&self, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution (optional)
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}
```

**Key Points:**
- Single-word method naming: `act()` and `validate()`
- No serialization methods - handled internally by rkyv
- Subscriptions managed through `SubscriptionManager` parameter
- Associated `Reaction` type for type safety

### Reaction Trait - STABLE API

```rust
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    fn remember(&self) -> Vec<MemoryOperation>;
}
```

**Key Points:**
- Single method: `remember()` returns memory operations
- No serialization methods - handled internally by rkyv
- Pure event sourcing - reactions define state changes

### RemoteReaction Trait - STABLE API

```rust
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate the remote reaction before processing (optional)
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all remote reactions
    }

    /// Return memory operations to apply when this remote reaction arrives
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to this remote reaction after it has been applied to memory (optional)
    async fn react(&self, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no additional reaction
    }
}
```

**Key Points:**
- For handling reactions from remote actor subscriptions
- Three-phase processing: `validate()` → `remember()` → `react()`
- Developers import reaction types from remote actors and implement this trait

### Actor Trait - STABLE API

```rust
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories, subscriptions: &SubscriptionManager) -> TamtilResult<Vec<u8>>;

    /// Handle remote reaction from subscription (optional)
    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _memories: &ActorMemories) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    fn id(&self) -> &ActorId;
}
```

**Key Points:**
- Primarily implemented by `GenericActor<A: Action>`
- Handles serialization internally
- Supports subscription-based remote reactions

### GenericActor - STABLE API

```rust
pub struct GenericActor<A: Action> {
    // Internal fields
}

impl<A: Action> GenericActor<A> {
    /// Create new generic actor
    pub fn new(id: ActorId) -> Self;
}
```

**Key Points:**
- Generic over any `Action` type
- Automatically implements `Actor` trait
- Handles action processing and reaction generation

## Phase 2: Stabilized Memory and Subscription APIs ✅

### ActorMemories - STABLE API

```rust
impl ActorMemories {
    /// Recall a value by key
    pub async fn recall(&self, key: &str) -> TamtilResult<Option<Vec<u8>>>;

    /// Get counter value
    pub async fn get_counter(&self, key: &str) -> TamtilResult<i64>;

    /// Get list
    pub async fn get_list(&self, key: &str) -> TamtilResult<Vec<Vec<u8>>>;
}
```

**Key Points:**
- Read-only access for actions and reactions
- Three data types: key-value, counters, lists
- State changes only through `MemoryOperation`s

### MemoryOperation - STABLE API

```rust
#[derive(Debug, Clone)]
pub enum MemoryOperation {
    /// Set a key-value pair
    Set { key: String, value: Vec<u8> },
    /// Delete a key
    Delete { key: String },
    /// Increment a counter
    Increment { key: String, amount: i64 },
    /// Append to a list
    Append { key: String, value: Vec<u8> },
    /// Remove from a list
    Remove { key: String, index: usize },
}
```

**Key Points:**
- Atomic operations applied when reactions are processed
- Supports all common data operations
- Returned by `Reaction::remember()`

### Subscription Types - STABLE API

```rust
#[derive(Debug, Clone)]
pub struct Subscription {
    pub subscriber: ActorId,
    pub publisher: ActorId,
    pub reaction_type: String,
    pub created_at: u64,
}

#[derive(Debug, Clone)]
pub struct SubscribedReaction {
    pub reaction_bytes: Vec<u8>,
    pub publisher: ActorId,
    pub reaction_type: String,
    pub timestamp: u64,
}
```

**Key Points:**
- Actors subscribe to other actors' reactions
- Type-based subscription matching
- Automatic delivery to subscribers

## Phase 3: Stabilized Platform Setup APIs ✅

### Platform - STABLE API

```rust
impl Platform {
    /// Create local platform for CLI applications
    pub fn local(id: impl Into<String>) -> Self;

    /// Create distributed platform for web applications
    pub fn distributed(id: impl Into<String>, node_id: NodeId, peers: Vec<NodeId>) -> Self;

    /// Create new context
    pub async fn create_context(&self, context_id: impl Into<String>) -> TamtilResult<()>;

    /// Add actor to context
    pub async fn add_actor_to_context<A: Actor>(&self, context_id: &ActorId, actor: A) -> TamtilResult<()>;

    /// Send action to actor
    pub async fn send_action(&self, context_id: &ActorId, actor_id: &ActorId, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>>;
}
```

**Key Points:**
- Transparent local vs distributed execution
- Same API for CLI and web applications
- Context-based actor organization

### DeploymentMode - STABLE API

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DeploymentMode {
    /// Local mode for CLI applications (single machine, no consensus)
    Local,
    /// Distributed mode for web applications (multi-machine, with consensus)
    Distributed,
}
```

## Core Types - STABLE API

### ActorId - STABLE API

```rust
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ActorId {
    pub id: String,
}

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self;
}
```

### TamtilResult and TamtilError - STABLE API

```rust
pub type TamtilResult<T> = Result<T, TamtilError>;

#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },
    
    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },
    
    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },
    
    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },
    
    #[error("Validation failed: {message}")]
    Validation { message: String },
    
    // ... other error variants
}
```

## Developer Workflow - STABLE PATTERN

1. **Define Reactions First** - What state changes will occur
2. **Define Actions** - What business logic produces those reactions  
3. **Build Actor** - `GenericActor::new(actor_id)`
4. **Define Context** - Group related actors
5. **Define Platform** - `Platform::local()` or `Platform::distributed()`

## API Stability Guarantee

These APIs are **STABLE** and follow semantic versioning:

- **Patch versions** (0.1.1, 0.1.2): Bug fixes, no API changes
- **Minor versions** (0.2.0, 0.3.0): Additive changes, backward compatible
- **Major versions** (1.0.0, 2.0.0): Breaking changes allowed

## What's Intentionally NOT Stabilized

Internal TAMTIL features that can evolve without breaking user code:

- `consensus::*` - All internal consensus logic
- `actor::core::ActorHandle` - Internal implementation details
- `http::server::HttpServer` - Internal request handling
- Context's internal management - Orchestration details
- Platform's internal management - Context coordination

## Usage Example

```rust
use tamtil::*;

// Define your reaction
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
struct CounterReaction {
    new_value: i64,
}

impl Reaction for CounterReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![MemoryOperation::Set {
            key: "counter".to_string(),
            value: self.new_value.to_le_bytes().to_vec(),
        }]
    }
}

// Define your action
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
struct IncrementAction {
    amount: i64,
}

#[async_trait]
impl Action for IncrementAction {
    type Reaction = CounterReaction;

    async fn act(&self, memories: &ActorMemories, _subscriptions: &SubscriptionManager) -> TamtilResult<Self::Reaction> {
        let current = memories.get_counter("counter").await.unwrap_or(0);
        Ok(CounterReaction {
            new_value: current + self.amount,
        })
    }
}

// Set up platform and actors
#[tokio::main]
async fn main() -> TamtilResult<()> {
    let platform = Platform::local("my_app");
    platform.create_context("counters").await?;
    
    let actor = GenericActor::<IncrementAction>::new(ActorId::new("counter_actor"));
    platform.add_actor_to_context(&ActorId::new("counters"), actor).await?;
    
    Ok(())
}
```

This API provides a stable foundation for building complex actor-based applications while allowing TAMTIL's internal implementation to evolve and improve.
